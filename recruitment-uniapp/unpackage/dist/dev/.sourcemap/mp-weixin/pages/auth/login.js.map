{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?6e75", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?588c", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?fa6c", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?f676", "uni-app:///pages/auth/login.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?ae5e", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/auth/login.vue?7c81"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "wechatLoading", "phoneLoading", "manualLoading", "agreed", "showManual<PERSON><PERSON><PERSON>", "phoneForm", "phone", "code", "codeDisabled", "codeCountdown", "codeTimer", "computed", "canSubmit", "codeText", "onLoad", "onUnload", "clearInterval", "methods", "handleWechatLogin", "e", "loginRes", "authApi", "res", "auth", "console", "getWechatCode", "uni", "provider", "success", "fail", "handlePhoneLogin", "sendCode", "startCountdown", "handleManualLogin", "onAgreementChange", "showAgreement", "url", "redirectToHome", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4Gv1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA,0DACA,oCACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAIAC;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAEA;kBACA;kBACAC;kBACAA;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAIAX;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAEA;kBACA;kBACAC;kBACAA;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEA;QACA;QACA;UACAhB;UACA;QACA;MACA;IACA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBAAA;gBAAA,OAEAZ;cAAA;gBAAAC;gBAEA;kBACA;kBACAC;kBACAA;kBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MACA;IACA;IAEA;IACAC;MACA;MACAT;QAAAU;MAAA;IACA;IAEA;IACAC;MACAC;QACAZ;UACAU;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChUA;AAAA;AAAA;AAAA;AAA0jD,CAAgB,07CAAG,EAAC,C;;;;;;;;;;;ACA9kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/auth/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/auth/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=cbd6070a&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cbd6070a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/auth/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=cbd6070a&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showManualLogin = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showManualLogin = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"login-container\">\n    <!-- 头部logo -->\n    <view class=\"header\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n      <text class=\"app-name\">招聘平台</text>\n      <text class=\"slogan\">找工作，就上招聘平台</text>\n    </view>\n    \n    <!-- 登录方式选择 -->\n    <view class=\"login-methods\">\n      <!-- 微信授权登录 -->\n      <button \n        class=\"login-btn wechat-btn\" \n        open-type=\"getUserInfo\"\n        @getuserinfo=\"handleWechatLogin\"\n        :loading=\"wechatLoading\"\n      >\n        <text class=\"iconfont icon-wechat\"></text>\n        <text>微信一键登录</text>\n      </button>\n      \n      <!-- 手机号登录 -->\n      <button \n        class=\"login-btn phone-btn\" \n        open-type=\"getPhoneNumber\"\n        @getphonenumber=\"handlePhoneLogin\"\n        :loading=\"phoneLoading\"\n      >\n        <text class=\"iconfont icon-phone\"></text>\n        <text>手机号一键登录</text>\n      </button>\n      \n      <!-- 手动输入手机号 -->\n      <view class=\"manual-phone\" @click=\"showManualLogin = true\">\n        <text class=\"iconfont icon-mobile\"></text>\n        <text>手动输入手机号登录</text>\n      </view>\n    </view>\n    \n    <!-- 用户协议 -->\n    <view class=\"agreement\">\n      <checkbox-group @change=\"onAgreementChange\">\n        <checkbox :checked=\"agreed\" color=\"#007aff\" />\n      </checkbox-group>\n      <text class=\"agreement-text\">\n        我已阅读并同意\n        <text class=\"link\" @click=\"showAgreement('user')\">《用户协议》</text>\n        和\n        <text class=\"link\" @click=\"showAgreement('privacy')\">《隐私政策》</text>\n      </text>\n    </view>\n    \n    <!-- 手动输入手机号弹窗 -->\n    <u-popup v-model=\"showManualLogin\" mode=\"center\" width=\"80%\" border-radius=\"20\">\n      <view class=\"manual-login-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">手机号登录</text>\n          <text class=\"close-btn\" @click=\"showManualLogin = false\">×</text>\n        </view>\n        \n        <view class=\"form\">\n          <view class=\"form-item\">\n            <text class=\"label\">手机号</text>\n            <input \n              class=\"input\" \n              type=\"number\" \n              placeholder=\"请输入手机号\" \n              v-model=\"phoneForm.phone\"\n              maxlength=\"11\"\n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"label\">验证码</text>\n            <view class=\"code-input\">\n              <input \n                class=\"input\" \n                type=\"number\" \n                placeholder=\"请输入验证码\" \n                v-model=\"phoneForm.code\"\n                maxlength=\"6\"\n              />\n              <button \n                class=\"code-btn\" \n                :disabled=\"codeDisabled\"\n                @click=\"sendCode\"\n              >\n                {{ codeText }}\n              </button>\n            </view>\n          </view>\n          \n          <button \n            class=\"submit-btn\" \n            :disabled=\"!canSubmit\"\n            :loading=\"manualLoading\"\n            @click=\"handleManualLogin\"\n          >\n            登录\n          </button>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { authApi } from '@/utils/api.js'\nimport { auth, validatePhone, showToast, showLoading, hideLoading } from '@/utils/utils.js'\n\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      wechatLoading: false,\n      phoneLoading: false,\n      manualLoading: false,\n      agreed: false,\n      showManualLogin: false,\n      phoneForm: {\n        phone: '',\n        code: ''\n      },\n      codeDisabled: false,\n      codeCountdown: 0,\n      codeTimer: null\n    }\n  },\n  computed: {\n    canSubmit() {\n      return validatePhone(this.phoneForm.phone) && \n             this.phoneForm.code.length === 6 && \n             this.agreed\n    },\n    codeText() {\n      return this.codeCountdown > 0 ? `${this.codeCountdown}s` : '获取验证码'\n    }\n  },\n  onLoad() {\n    // 检查是否已登录\n    if (auth.isLoggedIn()) {\n      this.redirectToHome()\n    }\n  },\n  onUnload() {\n    if (this.codeTimer) {\n      clearInterval(this.codeTimer)\n    }\n  },\n  methods: {\n    // 微信授权登录\n    async handleWechatLogin(e) {\n      if (!this.agreed) {\n        showToast('请先同意用户协议和隐私政策')\n        return\n      }\n      \n      if (e.detail.errMsg !== 'getUserInfo:ok') {\n        showToast('微信授权失败')\n        return\n      }\n      \n      try {\n        this.wechatLoading = true\n        \n        // 获取微信登录code\n        const loginRes = await this.getWechatCode()\n        \n        // 调用登录接口\n        const res = await authApi.wechatLogin(loginRes.code)\n        \n        if (res.code === 200) {\n          // 保存登录信息\n          auth.setToken(res.data.token)\n          auth.setUserInfo(res.data.userInfo)\n          \n          showToast('登录成功', 'success')\n          this.redirectToHome()\n        } else {\n          showToast(res.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('微信登录失败:', error)\n        showToast('登录失败，请重试')\n      } finally {\n        this.wechatLoading = false\n      }\n    },\n    \n    // 获取微信登录code\n    getWechatCode() {\n      return new Promise((resolve, reject) => {\n        uni.login({\n          provider: 'weixin',\n          success: resolve,\n          fail: reject\n        })\n      })\n    },\n    \n    // 手机号一键登录\n    async handlePhoneLogin(e) {\n      if (!this.agreed) {\n        showToast('请先同意用户协议和隐私政策')\n        return\n      }\n      \n      if (e.detail.errMsg !== 'getPhoneNumber:ok') {\n        showToast('获取手机号失败')\n        return\n      }\n      \n      try {\n        this.phoneLoading = true\n\n        // 获取微信登录code获取sessionKey\n        const loginRes = await this.getWechatCode()\n\n        // 调用登录接口\n        const res = await authApi.phoneLoginByWechat(e.detail.encryptedData, e.detail.iv, loginRes.code)\n\n        if (res.code === 200) {\n          // 保存登录信息\n          auth.setToken(res.data.token)\n          auth.setUserInfo(res.data.userInfo)\n\n          showToast('登录成功', 'success')\n          this.redirectToHome()\n        } else {\n          showToast(res.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('手机号登录失败:', error)\n        showToast('登录失败，请重试')\n      } finally {\n        this.phoneLoading = false\n      }\n    },\n    \n    // 发送验证码\n    async sendCode() {\n      if (!validatePhone(this.phoneForm.phone)) {\n        showToast('请输入正确的手机号')\n        return\n      }\n      \n      try {\n        // 这里应该调用发送验证码的接口\n        // await authApi.sendCode(this.phoneForm.phone)\n        \n        showToast('验证码已发送')\n        this.startCountdown()\n      } catch (error) {\n        showToast('发送验证码失败')\n      }\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      this.codeDisabled = true\n      this.codeCountdown = 60\n      \n      this.codeTimer = setInterval(() => {\n        this.codeCountdown--\n        if (this.codeCountdown <= 0) {\n          clearInterval(this.codeTimer)\n          this.codeDisabled = false\n        }\n      }, 1000)\n    },\n    \n    // 手动输入手机号登录\n    async handleManualLogin() {\n      if (!this.canSubmit) return\n      \n      try {\n        this.manualLoading = true\n        \n        const res = await authApi.phoneLogin(this.phoneForm.phone, this.phoneForm.code)\n        \n        if (res.code === 200) {\n          // 保存登录信息\n          auth.setToken(res.data.token)\n          auth.setUserInfo(res.data.userInfo)\n          \n          showToast('登录成功', 'success')\n          this.showManualLogin = false\n          this.redirectToHome()\n        } else {\n          showToast(res.msg || '登录失败')\n        }\n      } catch (error) {\n        console.error('手动登录失败:', error)\n        showToast('登录失败，请重试')\n      } finally {\n        this.manualLoading = false\n      }\n    },\n    \n    // 同意协议变化\n    onAgreementChange(e) {\n      this.agreed = e.detail.value.length > 0\n    },\n    \n    // 显示协议\n    showAgreement(type) {\n      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'\n      uni.navigateTo({ url })\n    },\n    \n    // 跳转到首页\n    redirectToHome() {\n      setTimeout(() => {\n        uni.switchTab({\n          url: '/pages/index/index'\n        })\n      }, 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 100rpx 60rpx 60rpx;\n  \n  .header {\n    text-align: center;\n    margin-bottom: 120rpx;\n    \n    .logo {\n      width: 120rpx;\n      height: 120rpx;\n      margin-bottom: 40rpx;\n    }\n    \n    .app-name {\n      display: block;\n      font-size: 48rpx;\n      font-weight: 600;\n      color: #fff;\n      margin-bottom: 20rpx;\n    }\n    \n    .slogan {\n      display: block;\n      font-size: 28rpx;\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n  \n  .login-methods {\n    margin-bottom: 80rpx;\n    \n    .login-btn {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 100%;\n      height: 88rpx;\n      border-radius: 44rpx;\n      font-size: 32rpx;\n      font-weight: 500;\n      margin-bottom: 32rpx;\n      border: none;\n      \n      .iconfont {\n        font-size: 36rpx;\n        margin-right: 16rpx;\n      }\n      \n      &.wechat-btn {\n        background: #07c160;\n        color: #fff;\n      }\n      \n      &.phone-btn {\n        background: #fff;\n        color: #333;\n      }\n    }\n    \n    .manual-phone {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 88rpx;\n      border: 2rpx solid rgba(255, 255, 255, 0.3);\n      border-radius: 44rpx;\n      font-size: 32rpx;\n      color: #fff;\n      \n      .iconfont {\n        font-size: 36rpx;\n        margin-right: 16rpx;\n      }\n    }\n  }\n  \n  .agreement {\n    display: flex;\n    align-items: flex-start;\n    font-size: 24rpx;\n    color: rgba(255, 255, 255, 0.8);\n    \n    checkbox-group {\n      margin-right: 16rpx;\n      margin-top: 4rpx;\n    }\n    \n    .agreement-text {\n      flex: 1;\n      line-height: 1.5;\n      \n      .link {\n        color: #fff;\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n.manual-login-popup {\n  padding: 40rpx;\n  \n  .popup-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 40rpx;\n    \n    .popup-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n    \n    .close-btn {\n      font-size: 48rpx;\n      color: #999;\n      line-height: 1;\n    }\n  }\n  \n  .form {\n    .form-item {\n      margin-bottom: 32rpx;\n      \n      .label {\n        display: block;\n        font-size: 28rpx;\n        color: #333;\n        margin-bottom: 16rpx;\n      }\n      \n      .input {\n        width: 100%;\n        height: 80rpx;\n        padding: 0 24rpx;\n        border: 2rpx solid #e9ecef;\n        border-radius: 12rpx;\n        font-size: 28rpx;\n        background: #f8f9fa;\n      }\n      \n      .code-input {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n        \n        .input {\n          flex: 1;\n        }\n        \n        .code-btn {\n          width: 200rpx;\n          height: 80rpx;\n          background: #007aff;\n          color: #fff;\n          border: none;\n          border-radius: 12rpx;\n          font-size: 24rpx;\n          \n          &:disabled {\n            background: #ccc;\n          }\n        }\n      }\n    }\n    \n    .submit-btn {\n      width: 100%;\n      height: 88rpx;\n      background: #007aff;\n      color: #fff;\n      border: none;\n      border-radius: 44rpx;\n      font-size: 32rpx;\n      font-weight: 500;\n      margin-top: 40rpx;\n      \n      &:disabled {\n        background: #ccc;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756771055877\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}