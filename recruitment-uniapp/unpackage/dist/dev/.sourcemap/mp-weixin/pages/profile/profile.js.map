{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?c8d3", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?7575", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?2e51", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?fe5a", "uni-app:///pages/profile/profile.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?75f4", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/profile.vue?939a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userInfo", "membershipInfo", "isMember", "remainingViews", "collectCount", "applicationCount", "showAboutModal", "computed", "isLoggedIn", "onShow", "methods", "loadUserInfo", "loadMembershipInfo", "paymentApi", "res", "console", "loadStatistics", "goToLogin", "uni", "url", "goToMemberCenter", "goToConsumptionRecords", "goToCollections", "goToApplications", "goToSettings", "contactService", "title", "content", "confirmText", "success", "showAbout", "handleLogout", "confirmed", "auth"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACiKz1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAE;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACA;QACA;QACA;MACA;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;QACA;QACA;MACA;MACAJ;QACAC;MACA;IACA;IAEA;IACAI;MACA;QACA;QACA;MACA;MACAL;QACAC;MACA;IACA;IAEA;IACAK;MACAN;QACAC;MACA;IACA;IAEA;IACAM;MACAP;QACAQ;QACAC;QACAC;QACAC;UACA;YACAX;cACAnB;cACA8B;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAA4jD,CAAgB,47CAAG,EAAC,C;;;;;;;;;;;ACAhlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=60cb334c&scoped=true&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&id=60cb334c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"60cb334c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=60cb334c&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showAboutModal = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 用户信息卡片 -->\n    <view class=\"user-card\">\n      <view class=\"user-info\" @click=\"goToLogin\" v-if=\"!isLoggedIn\">\n        <view class=\"avatar\">\n          <image src=\"/static/default-avatar.png\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"user-details\">\n          <text class=\"username\">点击登录</text>\n          <text class=\"user-desc\">登录后享受更多服务</text>\n        </view>\n        <text class=\"iconfont icon-arrow-right\"></text>\n      </view>\n      \n      <view class=\"user-info\" v-else>\n        <view class=\"avatar\">\n          <image :src=\"userInfo.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"user-details\">\n          <text class=\"username\">{{ userInfo.name || '用户' }}</text>\n          <text class=\"user-desc\">{{ userInfo.phone || '未绑定手机号' }}</text>\n        </view>\n        <view class=\"member-badge\" v-if=\"membershipInfo.isMember\">\n          <text>会员</text>\n        </view>\n      </view>\n      \n      <!-- 统计信息 -->\n      <view class=\"stats-info\" v-if=\"isLoggedIn\">\n        <view class=\"stats-item\" @click=\"goToConsumptionRecords\">\n          <text class=\"stats-value\">{{ membershipInfo.remainingViews || 0 }}</text>\n          <text class=\"stats-label\">剩余次数</text>\n        </view>\n        <view class=\"stats-item\" @click=\"goToCollections\">\n          <text class=\"stats-value\">{{ collectCount }}</text>\n          <text class=\"stats-label\">收藏职位</text>\n        </view>\n        <view class=\"stats-item\" @click=\"goToApplications\">\n          <text class=\"stats-value\">{{ applicationCount }}</text>\n          <text class=\"stats-label\">投递记录</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <view class=\"menu-title\">我的服务</view>\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"goToMemberCenter\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-vip\"></text>\n          </view>\n          <text class=\"menu-text\">会员中心</text>\n          <view class=\"menu-badge\" v-if=\"!membershipInfo.isMember\">\n            <text>开通</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToConsumptionRecords\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-bill\"></text>\n          </view>\n          <text class=\"menu-text\">消费记录</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToCollections\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-heart\"></text>\n          </view>\n          <text class=\"menu-text\">收藏职位</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToApplications\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-send\"></text>\n          </view>\n          <text class=\"menu-text\">投递记录</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 工具菜单 -->\n    <view class=\"menu-section\">\n      <view class=\"menu-title\">工具</view>\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"goToSettings\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-settings\"></text>\n          </view>\n          <text class=\"menu-text\">设置</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"contactService\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-service\"></text>\n          </view>\n          <text class=\"menu-text\">客服咨询</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"showAbout\">\n          <view class=\"menu-icon\">\n            <text class=\"iconfont icon-info\"></text>\n          </view>\n          <text class=\"menu-text\">关于我们</text>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 退出登录 -->\n    <view class=\"logout-section\" v-if=\"isLoggedIn\">\n      <button class=\"logout-btn\" @click=\"handleLogout\">退出登录</button>\n    </view>\n    \n    <!-- 关于我们弹窗 -->\n    <u-popup v-model=\"showAboutModal\" mode=\"center\" width=\"80%\" border-radius=\"20\">\n      <view class=\"about-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">关于我们</text>\n          <text class=\"close-btn\" @click=\"showAboutModal = false\">×</text>\n        </view>\n        \n        <view class=\"about-content\">\n          <view class=\"app-info\">\n            <image src=\"/static/logo.png\" class=\"app-logo\"></image>\n            <text class=\"app-name\">招聘平台</text>\n            <text class=\"app-version\">版本 1.0.0</text>\n          </view>\n          \n          <view class=\"app-desc\">\n            <text>专业的招聘信息平台，为求职者提供优质的工作机会，为企业提供精准的人才匹配服务。</text>\n          </view>\n          \n          <view class=\"contact-info\">\n            <view class=\"contact-item\">\n              <text class=\"label\">客服电话：</text>\n              <text class=\"value\">400-888-8888</text>\n            </view>\n            <view class=\"contact-item\">\n              <text class=\"label\">客服微信：</text>\n              <text class=\"value\">kf888888</text>\n            </view>\n            <view class=\"contact-item\">\n              <text class=\"label\">官方网站：</text>\n              <text class=\"value\">www.recruitment.com</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { paymentApi } from '@/utils/api.js'\nimport { auth, showToast, showConfirm } from '@/utils/utils.js'\n\nexport default {\n  name: 'Profile',\n  data() {\n    return {\n      userInfo: {},\n      membershipInfo: {\n        isMember: false,\n        remainingViews: 0\n      },\n      collectCount: 0,\n      applicationCount: 0,\n      showAboutModal: false\n    }\n  },\n  computed: {\n    isLoggedIn() {\n      return auth.isLoggedIn()\n    }\n  },\n  onShow() {\n    if (this.isLoggedIn) {\n      this.loadUserInfo()\n      this.loadMembershipInfo()\n      this.loadStatistics()\n    }\n  },\n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      this.userInfo = auth.getUserInfo() || {}\n    },\n    \n    // 加载会员信息\n    async loadMembershipInfo() {\n      try {\n        const res = await paymentApi.getMembershipInfo()\n        if (res.code === 200) {\n          this.membershipInfo = res.data\n        }\n      } catch (error) {\n        console.error('加载会员信息失败:', error)\n      }\n    },\n    \n    // 加载统计信息\n    async loadStatistics() {\n      try {\n        // 这里可以调用统计接口\n        // const res = await userApi.getStatistics()\n        // 临时使用模拟数据\n        this.collectCount = 12\n        this.applicationCount = 8\n      } catch (error) {\n        console.error('加载统计信息失败:', error)\n      }\n    },\n    \n    // 跳转到登录页\n    goToLogin() {\n      uni.navigateTo({\n        url: '/pages/auth/login'\n      })\n    },\n    \n    // 跳转到会员中心\n    goToMemberCenter() {\n      if (!this.isLoggedIn) {\n        this.goToLogin()\n        return\n      }\n      uni.switchTab({\n        url: '/pages/member/center'\n      })\n    },\n    \n    // 跳转到消费记录\n    goToConsumptionRecords() {\n      if (!this.isLoggedIn) {\n        this.goToLogin()\n        return\n      }\n      uni.navigateTo({\n        url: '/pages/member/consumption-records'\n      })\n    },\n    \n    // 跳转到收藏职位\n    goToCollections() {\n      if (!this.isLoggedIn) {\n        this.goToLogin()\n        return\n      }\n      uni.navigateTo({\n        url: '/pages/profile/collections'\n      })\n    },\n    \n    // 跳转到投递记录\n    goToApplications() {\n      if (!this.isLoggedIn) {\n        this.goToLogin()\n        return\n      }\n      uni.navigateTo({\n        url: '/pages/profile/applications'\n      })\n    },\n    \n    // 跳转到设置\n    goToSettings() {\n      uni.navigateTo({\n        url: '/pages/profile/settings'\n      })\n    },\n    \n    // 联系客服\n    contactService() {\n      uni.showModal({\n        title: '客服咨询',\n        content: '请添加客服微信：kf888888',\n        confirmText: '复制微信号',\n        success: (res) => {\n          if (res.confirm) {\n            uni.setClipboardData({\n              data: 'kf888888',\n              success: () => {\n                showToast('微信号已复制')\n              }\n            })\n          }\n        }\n      })\n    },\n    \n    // 显示关于我们\n    showAbout() {\n      this.showAboutModal = true\n    },\n    \n    // 退出登录\n    async handleLogout() {\n      const confirmed = await showConfirm('确定要退出登录吗？')\n      if (confirmed) {\n        auth.logout()\n        showToast('已退出登录')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  \n  .user-card {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    padding: 60rpx 40rpx 40rpx;\n    \n    .user-info {\n      display: flex;\n      align-items: center;\n      margin-bottom: 40rpx;\n      \n      .avatar {\n        width: 100rpx;\n        height: 100rpx;\n        border-radius: 50rpx;\n        overflow: hidden;\n        margin-right: 24rpx;\n        \n        image {\n          width: 100%;\n          height: 100%;\n        }\n      }\n      \n      .user-details {\n        flex: 1;\n        \n        .username {\n          display: block;\n          font-size: 36rpx;\n          font-weight: 600;\n          color: #fff;\n          margin-bottom: 8rpx;\n        }\n        \n        .user-desc {\n          display: block;\n          font-size: 24rpx;\n          color: rgba(255, 255, 255, 0.8);\n        }\n      }\n      \n      .member-badge {\n        background: rgba(255, 255, 255, 0.2);\n        color: #fff;\n        font-size: 20rpx;\n        padding: 8rpx 16rpx;\n        border-radius: 16rpx;\n        margin-right: 16rpx;\n      }\n      \n      .icon-arrow-right {\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n      }\n    }\n    \n    .stats-info {\n      display: flex;\n      justify-content: space-around;\n      \n      .stats-item {\n        text-align: center;\n        \n        .stats-value {\n          display: block;\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #fff;\n          margin-bottom: 8rpx;\n        }\n        \n        .stats-label {\n          display: block;\n          font-size: 24rpx;\n          color: rgba(255, 255, 255, 0.8);\n        }\n      }\n    }\n  }\n  \n  .menu-section {\n    margin: 40rpx;\n    \n    .menu-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n    \n    .menu-list {\n      background: #fff;\n      border-radius: 16rpx;\n      overflow: hidden;\n      \n      .menu-item {\n        display: flex;\n        align-items: center;\n        padding: 32rpx;\n        border-bottom: 2rpx solid #f8f9fa;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .menu-icon {\n          width: 72rpx;\n          height: 72rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background: #f0f8ff;\n          border-radius: 36rpx;\n          margin-right: 24rpx;\n          \n          .iconfont {\n            font-size: 32rpx;\n            color: #007aff;\n          }\n        }\n        \n        .menu-text {\n          flex: 1;\n          font-size: 28rpx;\n          color: #333;\n        }\n        \n        .menu-badge {\n          background: #ff4757;\n          color: #fff;\n          font-size: 20rpx;\n          padding: 6rpx 12rpx;\n          border-radius: 12rpx;\n          margin-right: 16rpx;\n        }\n        \n        .icon-arrow-right {\n          font-size: 24rpx;\n          color: #ccc;\n        }\n      }\n    }\n  }\n  \n  .logout-section {\n    margin: 40rpx;\n    \n    .logout-btn {\n      width: 100%;\n      height: 88rpx;\n      background: #fff;\n      color: #ff4757;\n      border: 2rpx solid #ff4757;\n      border-radius: 44rpx;\n      font-size: 28rpx;\n    }\n  }\n}\n\n.about-modal {\n  padding: 40rpx;\n  \n  .modal-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 40rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n    \n    .close-btn {\n      font-size: 48rpx;\n      color: #999;\n      line-height: 1;\n    }\n  }\n  \n  .about-content {\n    .app-info {\n      text-align: center;\n      margin-bottom: 40rpx;\n      \n      .app-logo {\n        width: 120rpx;\n        height: 120rpx;\n        margin-bottom: 24rpx;\n      }\n      \n      .app-name {\n        display: block;\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 8rpx;\n      }\n      \n      .app-version {\n        display: block;\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n    \n    .app-desc {\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 40rpx;\n    }\n    \n    .contact-info {\n      .contact-item {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        .label {\n          width: 160rpx;\n          font-size: 28rpx;\n          color: #666;\n        }\n        \n        .value {\n          flex: 1;\n          font-size: 28rpx;\n          color: #333;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=60cb334c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=60cb334c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756771055874\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}