{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/job/detail.vue?48d0", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/job/detail.vue?0564", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/job/detail.vue?dc9b", "uni-app:///pages/job/detail.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/job/detail.vue?e2e9", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/job/detail.vue?6076"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "jobId", "jobDetail", "isCollected", "showContactModal", "contactInfo", "visible", "contactName", "phone", "wechat", "membershipInfo", "isMember", "remainingViews", "payLoading", "onLoad", "onShareAppMessage", "title", "path", "methods", "formatSalary", "formatTime", "loadJobDetail", "jobApi", "res", "uni", "console", "loadMembershipInfo", "paymentApi", "viewCompany", "url", "viewMap", "latitude", "longitude", "address", "toggleCollect", "auth", "viewContact", "payToView", "buyMembership", "wechatPay", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "resolve", "fail", "reject", "makeCall", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqKx1B;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAEA;kBACA;kBACA;kBACAC;oBACAR;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAS;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAJ;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACAJ;QACAK;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEAN;QACAO;QACAC;QACAjC;QACAkC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACAX;kBACAK;gBACA;gBAAA;cAAA;gBAIA;kBACA;kBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAO;MACA;QACAZ;UACAK;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBAAA;gBAAA,OAEAV;cAAA;gBAAAJ;gBAEA;kBACA;oBACAjB;kBAAA,GACAiB,SACA;;kBAEA;kBACA;oBACA;kBACA;kBAEA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAX;cAAA;gBAAAJ;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAf;oBACAgB;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBACA;sBACA;sBACAC;oBACA;oBACAC;sBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA1B;QACA2B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAA2jD,CAAgB,27CAAG,EAAC,C;;;;;;;;;;;ACA/kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/job/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/job/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=dcc1a2c8&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=dcc1a2c8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dcc1a2c8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/job/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=dcc1a2c8&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatSalary(\n    _vm.jobDetail.salaryMin,\n    _vm.jobDetail.salaryMax,\n    _vm.jobDetail.salaryUnit\n  )\n  var m1 = _vm.formatTime(_vm.jobDetail.publishTime)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showContactModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"job-detail-container\">\n    <!-- 职位基本信息 -->\n    <view class=\"job-header\">\n      <view class=\"job-info\">\n        <text class=\"job-title\">{{ jobDetail.positionTitle }}</text>\n        <text class=\"salary\">{{ formatSalary(jobDetail.salaryMin, jobDetail.salaryMax, jobDetail.salaryUnit) }}</text>\n        <view class=\"job-tags\">\n          <text class=\"tag\">{{ jobDetail.industry }}</text>\n          <text class=\"tag\">{{ jobDetail.workNature }}</text>\n          <text class=\"tag\" v-if=\"jobDetail.isUrgent\">紧急</text>\n          <text class=\"tag\" v-if=\"jobDetail.isTop\">置顶</text>\n        </view>\n      </view>\n      <view class=\"company-logo\">\n        <image\n          :src=\"jobDetail.companyLogo || '/static/default-company.png'\"\n          mode=\"aspectFill\"\n        ></image>\n      </view>\n    </view>\n    \n    <!-- 职位要求 -->\n    <view class=\"job-requirements\">\n      <view class=\"requirement-item\">\n        <text class=\"iconfont icon-location\"></text>\n        <text>{{ jobDetail.workAddress }}</text>\n      </view>\n      <view class=\"requirement-item\">\n        <text class=\"iconfont icon-experience\"></text>\n        <text>{{ jobDetail.workExperience || '经验不限' }}</text>\n      </view>\n      <view class=\"requirement-item\">\n        <text class=\"iconfont icon-education\"></text>\n        <text>{{ jobDetail.education || '学历不限' }}</text>\n      </view>\n      <view class=\"requirement-item\">\n        <text class=\"iconfont icon-time\"></text>\n        <text>{{ jobDetail.workNature }}</text>\n      </view>\n    </view>\n    \n    <!-- 公司信息 -->\n    <view class=\"company-info\">\n      <view class=\"company-header\">\n        <view class=\"company-basic\">\n          <text class=\"company-name\">{{ jobDetail.companyName }}</text>\n          <text class=\"company-scale\">{{ jobDetail.companyScale }}</text>\n        </view>\n        <text class=\"view-company\" @click=\"viewCompany\">查看详情</text>\n      </view>\n      <view class=\"company-desc\">\n        <text>{{ jobDetail.companyDesc }}</text>\n      </view>\n    </view>\n    \n    <!-- 职位描述 -->\n    <view class=\"job-description\">\n      <view class=\"section-title\">职位描述</view>\n      <view class=\"description-content\">\n        <rich-text :nodes=\"jobDetail.jobDescription\"></rich-text>\n      </view>\n    </view>\n    \n    <!-- 职位要求 -->\n    <view class=\"job-requirements-detail\" v-if=\"jobDetail.jobRequirements\">\n      <view class=\"section-title\">职位要求</view>\n      <view class=\"requirements-content\">\n        <rich-text :nodes=\"jobDetail.jobRequirements\"></rich-text>\n      </view>\n    </view>\n\n    <!-- 薪资待遇 -->\n    <view class=\"welfare-benefits\" v-if=\"jobDetail.welfareBenefits\">\n      <view class=\"section-title\">薪资待遇</view>\n      <view class=\"benefits-content\">\n        <rich-text :nodes=\"jobDetail.welfareBenefits\"></rich-text>\n      </view>\n    </view>\n    \n    <!-- 工作地址 -->\n    <view class=\"work-address\" v-if=\"jobDetail.workAddress\">\n      <view class=\"section-title\">工作地址</view>\n      <view class=\"address-info\">\n        <view class=\"address-text\">\n          <text class=\"iconfont icon-location\"></text>\n          <text>{{ jobDetail.workAddress }}</text>\n        </view>\n        <text class=\"view-map\" @click=\"viewMap\" v-if=\"jobDetail.latitude && jobDetail.longitude\">查看地图</text>\n      </view>\n    </view>\n    \n    <!-- 发布信息 -->\n    <view class=\"publish-info\">\n      <text class=\"publish-time\">发布时间：{{ formatTime(jobDetail.publishTime) }}</text>\n      <text class=\"view-count\">浏览：{{ jobDetail.viewCount }}次</text>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <button class=\"collect-btn\" :class=\"{ collected: isCollected }\" @click=\"toggleCollect\">\n        <text class=\"iconfont\" :class=\"isCollected ? 'icon-heart-fill' : 'icon-heart'\"></text>\n        <text>{{ isCollected ? '已收藏' : '收藏' }}</text>\n      </button>\n      <button class=\"contact-btn\" @click=\"viewContact\">\n        <text class=\"iconfont icon-phone\"></text>\n        <text>查看联系方式</text>\n      </button>\n    </view>\n    \n    <!-- 查看联系方式弹窗 -->\n    <u-popup v-model=\"showContactModal\" mode=\"center\" width=\"80%\" border-radius=\"20\">\n      <view class=\"contact-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">联系方式</text>\n          <text class=\"close-btn\" @click=\"showContactModal = false\">×</text>\n        </view>\n        \n        <view v-if=\"contactInfo.visible\" class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"label\">联系人：</text>\n            <text class=\"value\">{{ contactInfo.contactName }}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"label\">电话：</text>\n            <text class=\"value phone\" @click=\"makeCall(contactInfo.phone)\">{{ contactInfo.phone }}</text>\n          </view>\n          <view class=\"contact-item\" v-if=\"contactInfo.wechat\">\n            <text class=\"label\">微信：</text>\n            <text class=\"value\">{{ contactInfo.wechat }}</text>\n          </view>\n        </view>\n        \n        <view v-else class=\"payment-content\">\n          <view class=\"payment-info\">\n            <text class=\"payment-title\">查看联系方式</text>\n            <text class=\"payment-desc\">支付1元即可查看该职位的联系方式</text>\n          </view>\n          \n          <view class=\"membership-tip\" v-if=\"!membershipInfo.isMember\">\n            <text class=\"tip-text\">成为会员更划算</text>\n            <view class=\"membership-options\">\n              <view class=\"membership-item\" @click=\"buyMembership('basic')\">\n                <text class=\"price\">¥30</text>\n                <text class=\"desc\">查看45个联系人</text>\n              </view>\n              <view class=\"membership-item\" @click=\"buyMembership('premium')\">\n                <text class=\"price\">¥50</text>\n                <text class=\"desc\">查看80个联系人</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"payment-actions\">\n            <button class=\"pay-btn\" @click=\"payToView\" :loading=\"payLoading\">\n              {{ membershipInfo.isMember ? '使用会员次数查看' : '支付1元查看' }}\n            </button>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { jobApi, paymentApi } from '@/utils/api.js'\nimport { formatSalary, formatTime, showToast, showLoading, hideLoading, auth } from '@/utils/utils.js'\n\nexport default {\n  name: 'JobDetail',\n  data() {\n    return {\n      jobId: '',\n      jobDetail: {},\n      isCollected: false,\n      showContactModal: false,\n      contactInfo: {\n        visible: false,\n        contactName: '',\n        phone: '',\n        wechat: ''\n      },\n      membershipInfo: {\n        isMember: false,\n        remainingViews: 0\n      },\n      payLoading: false\n    }\n  },\n  onLoad(options) {\n    this.jobId = options.id\n    if (this.jobId) {\n      this.loadJobDetail()\n      this.loadMembershipInfo()\n    }\n  },\n  onShareAppMessage() {\n    return {\n      title: this.jobDetail.positionTitle,\n      path: `/pages/job/detail?id=${this.jobId}`\n    }\n  },\n  methods: {\n    formatSalary,\n    formatTime,\n    \n    // 加载职位详情\n    async loadJobDetail() {\n      try {\n        showLoading()\n        const res = await jobApi.getJobDetail(this.jobId)\n        \n        if (res.code === 200) {\n          this.jobDetail = res.data\n          // 设置页面标题\n          uni.setNavigationBarTitle({\n            title: this.jobDetail.positionTitle\n          })\n        } else {\n          showToast(res.msg || '加载失败')\n        }\n      } catch (error) {\n        console.error('加载职位详情失败:', error)\n        showToast('加载失败，请重试')\n      } finally {\n        hideLoading()\n      }\n    },\n    \n    // 加载会员信息\n    async loadMembershipInfo() {\n      try {\n        const res = await paymentApi.getMembershipInfo()\n        if (res.code === 200) {\n          this.membershipInfo = res.data\n        }\n      } catch (error) {\n        console.error('加载会员信息失败:', error)\n      }\n    },\n    \n    // 查看公司详情\n    viewCompany() {\n      uni.navigateTo({\n        url: `/pages/company/detail?id=${this.jobDetail.companyId}`\n      })\n    },\n    \n    // 查看地图\n    viewMap() {\n      if (!this.jobDetail.latitude || !this.jobDetail.longitude) {\n        showToast('暂无位置信息')\n        return\n      }\n\n      uni.openLocation({\n        latitude: parseFloat(this.jobDetail.latitude),\n        longitude: parseFloat(this.jobDetail.longitude),\n        name: this.jobDetail.companyName,\n        address: this.jobDetail.workAddress\n      })\n    },\n    \n    // 切换收藏状态\n    async toggleCollect() {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      try {\n        // 这里调用收藏/取消收藏接口\n        this.isCollected = !this.isCollected\n        showToast(this.isCollected ? '收藏成功' : '取消收藏')\n      } catch (error) {\n        showToast('操作失败')\n      }\n    },\n    \n    // 查看联系方式\n    viewContact() {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      this.showContactModal = true\n    },\n    \n    // 支付查看联系方式\n    async payToView() {\n      if (this.payLoading) return\n      \n      try {\n        this.payLoading = true\n        \n        const res = await paymentApi.viewContact(this.jobId)\n        \n        if (res.code === 200) {\n          this.contactInfo = {\n            visible: true,\n            ...res.data\n          }\n          \n          // 更新会员信息\n          if (this.membershipInfo.isMember) {\n            this.membershipInfo.remainingViews--\n          }\n          \n          showToast('获取成功')\n        } else {\n          showToast(res.msg || '获取失败')\n        }\n      } catch (error) {\n        console.error('获取联系方式失败:', error)\n        showToast('获取失败，请重试')\n      } finally {\n        this.payLoading = false\n      }\n    },\n    \n    // 购买会员\n    async buyMembership(type) {\n      try {\n        const res = await paymentApi.buyMembership(type)\n        \n        if (res.code === 200) {\n          // 调用微信支付\n          await this.wechatPay(res.data)\n        } else {\n          showToast(res.msg || '购买失败')\n        }\n      } catch (error) {\n        console.error('购买会员失败:', error)\n        showToast('购买失败，请重试')\n      }\n    },\n    \n    // 微信支付\n    async wechatPay(paymentData) {\n      return new Promise((resolve, reject) => {\n        uni.requestPayment({\n          provider: 'wxpay',\n          timeStamp: paymentData.timeStamp,\n          nonceStr: paymentData.nonceStr,\n          package: paymentData.package,\n          signType: paymentData.signType,\n          paySign: paymentData.paySign,\n          success: (res) => {\n            showToast('支付成功', 'success')\n            this.loadMembershipInfo()\n            resolve(res)\n          },\n          fail: (err) => {\n            showToast('支付失败')\n            reject(err)\n          }\n        })\n      })\n    },\n    \n    // 拨打电话\n    makeCall(phone) {\n      uni.makePhoneCall({\n        phoneNumber: phone\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.job-detail-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 120rpx;\n  \n  .job-header {\n    background: #fff;\n    padding: 40rpx;\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n    \n    .job-info {\n      flex: 1;\n      \n      .job-title {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 16rpx;\n      }\n      \n      .salary {\n        display: block;\n        font-size: 32rpx;\n        color: #ff4757;\n        font-weight: 600;\n        margin-bottom: 24rpx;\n      }\n      \n      .job-tags {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 16rpx;\n        \n        .tag {\n          padding: 8rpx 16rpx;\n          background: #f0f8ff;\n          color: #007aff;\n          font-size: 24rpx;\n          border-radius: 8rpx;\n        }\n      }\n    }\n    \n    .company-logo {\n      width: 100rpx;\n      height: 100rpx;\n      border-radius: 16rpx;\n      overflow: hidden;\n      \n      image {\n        width: 100%;\n        height: 100%;\n      }\n    }\n  }\n  \n  .job-requirements {\n    background: #fff;\n    padding: 32rpx 40rpx;\n    margin-top: 20rpx;\n    display: flex;\n    flex-wrap: wrap;\n    gap: 32rpx;\n    \n    .requirement-item {\n      display: flex;\n      align-items: center;\n      font-size: 28rpx;\n      color: #666;\n      \n      .iconfont {\n        font-size: 28rpx;\n        margin-right: 12rpx;\n        color: #999;\n      }\n    }\n  }\n  \n  .company-info {\n    background: #fff;\n    padding: 32rpx 40rpx;\n    margin-top: 20rpx;\n    \n    .company-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 16rpx;\n      \n      .company-basic {\n        .company-name {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: #333;\n          margin-right: 16rpx;\n        }\n        \n        .company-scale {\n          font-size: 24rpx;\n          color: #666;\n        }\n      }\n      \n      .view-company {\n        font-size: 28rpx;\n        color: #007aff;\n      }\n    }\n    \n    .company-desc {\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.6;\n    }\n  }\n  \n  .job-description, .job-requirements-detail {\n    background: #fff;\n    padding: 32rpx 40rpx;\n    margin-top: 20rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n    \n    .description-content, .requirements-content {\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.8;\n    }\n  }\n  \n  .work-address {\n    background: #fff;\n    padding: 32rpx 40rpx;\n    margin-top: 20rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n    \n    .address-info {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .address-text {\n        display: flex;\n        align-items: center;\n        font-size: 28rpx;\n        color: #666;\n        \n        .iconfont {\n          font-size: 28rpx;\n          margin-right: 12rpx;\n          color: #999;\n        }\n      }\n      \n      .view-map {\n        font-size: 28rpx;\n        color: #007aff;\n      }\n    }\n  }\n  \n  .publish-info {\n    background: #fff;\n    padding: 32rpx 40rpx;\n    margin-top: 20rpx;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 24rpx;\n    color: #999;\n  }\n  \n  .bottom-actions {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    display: flex;\n    align-items: center;\n    padding: 24rpx 40rpx;\n    background: #fff;\n    border-top: 2rpx solid #f0f0f0;\n    \n    .collect-btn {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      width: 120rpx;\n      height: 80rpx;\n      background: transparent;\n      border: none;\n      font-size: 24rpx;\n      color: #666;\n      margin-right: 32rpx;\n      \n      &.collected {\n        color: #ff4757;\n      }\n      \n      .iconfont {\n        font-size: 32rpx;\n        margin-bottom: 8rpx;\n      }\n    }\n    \n    .contact-btn {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 80rpx;\n      background: #007aff;\n      color: #fff;\n      border: none;\n      border-radius: 40rpx;\n      font-size: 28rpx;\n      font-weight: 500;\n      \n      .iconfont {\n        font-size: 32rpx;\n        margin-right: 12rpx;\n      }\n    }\n  }\n}\n\n.contact-modal {\n  padding: 40rpx;\n  \n  .modal-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 40rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n    \n    .close-btn {\n      font-size: 48rpx;\n      color: #999;\n      line-height: 1;\n    }\n  }\n  \n  .contact-content {\n    .contact-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 24rpx;\n      \n      .label {\n        width: 120rpx;\n        font-size: 28rpx;\n        color: #666;\n      }\n      \n      .value {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333;\n        \n        &.phone {\n          color: #007aff;\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n  \n  .payment-content {\n    text-align: center;\n    \n    .payment-info {\n      margin-bottom: 40rpx;\n      \n      .payment-title {\n        display: block;\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 16rpx;\n      }\n      \n      .payment-desc {\n        display: block;\n        font-size: 28rpx;\n        color: #666;\n      }\n    }\n    \n    .membership-tip {\n      margin-bottom: 40rpx;\n      \n      .tip-text {\n        display: block;\n        font-size: 28rpx;\n        color: #333;\n        margin-bottom: 24rpx;\n      }\n      \n      .membership-options {\n        display: flex;\n        gap: 24rpx;\n        \n        .membership-item {\n          flex: 1;\n          padding: 24rpx;\n          background: #f8f9fa;\n          border-radius: 12rpx;\n          border: 2rpx solid #e9ecef;\n          \n          .price {\n            display: block;\n            font-size: 32rpx;\n            font-weight: 600;\n            color: #ff4757;\n            margin-bottom: 8rpx;\n          }\n          \n          .desc {\n            display: block;\n            font-size: 24rpx;\n            color: #666;\n          }\n        }\n      }\n    }\n    \n    .payment-actions {\n      .pay-btn {\n        width: 100%;\n        height: 80rpx;\n        background: #007aff;\n        color: #fff;\n        border: none;\n        border-radius: 40rpx;\n        font-size: 28rpx;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=dcc1a2c8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=dcc1a2c8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756772020332\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}