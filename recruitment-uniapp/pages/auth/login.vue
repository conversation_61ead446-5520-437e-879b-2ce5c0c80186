<template>
  <view class="login-container">
    <!-- 头部logo -->
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">招聘平台</text>
      <text class="slogan">找工作，就上招聘平台</text>
    </view>
    
    <!-- 登录方式选择 -->
    <view class="login-methods">
      <!-- 微信授权登录 -->
      <button 
        class="login-btn wechat-btn" 
        open-type="getUserInfo"
        @getuserinfo="handleWechatLogin"
        :loading="wechatLoading"
      >
        <text class="iconfont icon-wechat"></text>
        <text>微信一键登录</text>
      </button>
      
      <!-- 手机号登录 -->
      <button 
        class="login-btn phone-btn" 
        open-type="getPhoneNumber"
        @getphonenumber="handlePhoneLogin"
        :loading="phoneLoading"
      >
        <text class="iconfont icon-phone"></text>
        <text>手机号一键登录</text>
      </button>
      
      <!-- 手动输入手机号 -->
      <view class="manual-phone" @click="showManualLogin = true">
        <text class="iconfont icon-mobile"></text>
        <text>手动输入手机号登录</text>
      </view>
    </view>
    
    <!-- 用户协议 -->
    <view class="agreement">
      <checkbox-group @change="onAgreementChange">
        <checkbox :checked="agreed" color="#007aff" />
      </checkbox-group>
      <text class="agreement-text">
        我已阅读并同意
        <text class="link" @click="showAgreement('user')">《用户协议》</text>
        和
        <text class="link" @click="showAgreement('privacy')">《隐私政策》</text>
      </text>
    </view>
    
    <!-- 手动输入手机号弹窗 -->
    <u-popup v-model="showManualLogin" mode="center" width="80%" border-radius="20">
      <view class="manual-login-popup">
        <view class="popup-header">
          <text class="popup-title">手机号登录</text>
          <text class="close-btn" @click="showManualLogin = false">×</text>
        </view>
        
        <view class="form">
          <view class="form-item">
            <text class="label">手机号</text>
            <input 
              class="input" 
              type="number" 
              placeholder="请输入手机号" 
              v-model="phoneForm.phone"
              maxlength="11"
            />
          </view>
          
          <view class="form-item">
            <text class="label">验证码</text>
            <view class="code-input">
              <input 
                class="input" 
                type="number" 
                placeholder="请输入验证码" 
                v-model="phoneForm.code"
                maxlength="6"
              />
              <button 
                class="code-btn" 
                :disabled="codeDisabled"
                @click="sendCode"
              >
                {{ codeText }}
              </button>
            </view>
          </view>
          
          <button 
            class="submit-btn" 
            :disabled="!canSubmit"
            :loading="manualLoading"
            @click="handleManualLogin"
          >
            登录
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { authApi } from '@/utils/api.js'
import { auth, validatePhone, showToast, showLoading, hideLoading } from '@/utils/utils.js'

export default {
  name: 'Login',
  data() {
    return {
      wechatLoading: false,
      phoneLoading: false,
      manualLoading: false,
      agreed: false,
      showManualLogin: false,
      phoneForm: {
        phone: '',
        code: ''
      },
      codeDisabled: false,
      codeCountdown: 0,
      codeTimer: null
    }
  },
  computed: {
    canSubmit() {
      return validatePhone(this.phoneForm.phone) && 
             this.phoneForm.code.length === 6 && 
             this.agreed
    },
    codeText() {
      return this.codeCountdown > 0 ? `${this.codeCountdown}s` : '获取验证码'
    }
  },
  onLoad() {
    // 检查是否已登录
    if (auth.isLoggedIn()) {
      this.redirectToHome()
    }
  },
  onUnload() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
    }
  },
  methods: {
    // 微信授权登录
    async handleWechatLogin(e) {
      if (!this.agreed) {
        showToast('请先同意用户协议和隐私政策')
        return
      }
      
      if (e.detail.errMsg !== 'getUserInfo:ok') {
        showToast('微信授权失败')
        return
      }
      
      try {
        this.wechatLoading = true
        
        // 获取微信登录code
        const loginRes = await this.getWechatCode()
        
        // 调用登录接口
        const res = await authApi.wechatLogin(loginRes.code)
        
        if (res.code === 200) {
          // 保存登录信息
          auth.setToken(res.data.token)
          auth.setUserInfo(res.data.userInfo)
          
          showToast('登录成功', 'success')
          this.redirectToHome()
        } else {
          showToast(res.msg || '登录失败')
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        showToast('登录失败，请重试')
      } finally {
        this.wechatLoading = false
      }
    },
    
    // 获取微信登录code
    getWechatCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        })
      })
    },
    
    // 手机号一键登录
    async handlePhoneLogin(e) {
      if (!this.agreed) {
        showToast('请先同意用户协议和隐私政策')
        return
      }
      
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        showToast('获取手机号失败')
        return
      }
      
      try {
        this.phoneLoading = true

        // 获取微信登录code获取sessionKey
        const loginRes = await this.getWechatCode()

        // 调用登录接口
        const res = await authApi.phoneLoginByWechat(e.detail.encryptedData, e.detail.iv, loginRes.code)

        if (res.code === 200) {
          // 保存登录信息
          auth.setToken(res.data.token)
          auth.setUserInfo(res.data.userInfo)

          showToast('登录成功', 'success')
          this.redirectToHome()
        } else {
          showToast(res.msg || '登录失败')
        }
      } catch (error) {
        console.error('手机号登录失败:', error)
        showToast('登录失败，请重试')
      } finally {
        this.phoneLoading = false
      }
    },
    
    // 发送验证码
    async sendCode() {
      if (!validatePhone(this.phoneForm.phone)) {
        showToast('请输入正确的手机号')
        return
      }
      
      try {
        // 这里应该调用发送验证码的接口
        // await authApi.sendCode(this.phoneForm.phone)
        
        showToast('验证码已发送')
        this.startCountdown()
      } catch (error) {
        showToast('发送验证码失败')
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.codeDisabled = true
      this.codeCountdown = 60
      
      this.codeTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer)
          this.codeDisabled = false
        }
      }, 1000)
    },
    
    // 手动输入手机号登录
    async handleManualLogin() {
      if (!this.canSubmit) return
      
      try {
        this.manualLoading = true
        
        const res = await authApi.phoneLogin(this.phoneForm.phone, this.phoneForm.code)
        
        if (res.code === 200) {
          // 保存登录信息
          auth.setToken(res.data.token)
          auth.setUserInfo(res.data.userInfo)
          
          showToast('登录成功', 'success')
          this.showManualLogin = false
          this.redirectToHome()
        } else {
          showToast(res.msg || '登录失败')
        }
      } catch (error) {
        console.error('手动登录失败:', error)
        showToast('登录失败，请重试')
      } finally {
        this.manualLoading = false
      }
    },
    
    // 同意协议变化
    onAgreementChange(e) {
      this.agreed = e.detail.value.length > 0
    },
    
    // 显示协议
    showAgreement(type) {
      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'
      uni.navigateTo({ url })
    },
    
    // 跳转到首页
    redirectToHome() {
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 100rpx 60rpx 60rpx;
  
  .header {
    text-align: center;
    margin-bottom: 120rpx;
    
    .logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 40rpx;
    }
    
    .app-name {
      display: block;
      font-size: 48rpx;
      font-weight: 600;
      color: #fff;
      margin-bottom: 20rpx;
    }
    
    .slogan {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .login-methods {
    margin-bottom: 80rpx;
    
    .login-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 32rpx;
      border: none;
      
      .iconfont {
        font-size: 36rpx;
        margin-right: 16rpx;
      }
      
      &.wechat-btn {
        background: #07c160;
        color: #fff;
      }
      
      &.phone-btn {
        background: #fff;
        color: #333;
      }
    }
    
    .manual-phone {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #fff;
      
      .iconfont {
        font-size: 36rpx;
        margin-right: 16rpx;
      }
    }
  }
  
  .agreement {
    display: flex;
    align-items: flex-start;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    
    checkbox-group {
      margin-right: 16rpx;
      margin-top: 4rpx;
    }
    
    .agreement-text {
      flex: 1;
      line-height: 1.5;
      
      .link {
        color: #fff;
        text-decoration: underline;
      }
    }
  }
}

.manual-login-popup {
  padding: 40rpx;
  
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
  
  .form {
    .form-item {
      margin-bottom: 32rpx;
      
      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .input {
        width: 100%;
        height: 80rpx;
        padding: 0 24rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 12rpx;
        font-size: 28rpx;
        background: #f8f9fa;
      }
      
      .code-input {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .input {
          flex: 1;
        }
        
        .code-btn {
          width: 200rpx;
          height: 80rpx;
          background: #007aff;
          color: #fff;
          border: none;
          border-radius: 12rpx;
          font-size: 24rpx;
          
          &:disabled {
            background: #ccc;
          }
        }
      }
    }
    
    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: #007aff;
      color: #fff;
      border: none;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      margin-top: 40rpx;
      
      &:disabled {
        background: #ccc;
      }
    }
  }
}
</style>
