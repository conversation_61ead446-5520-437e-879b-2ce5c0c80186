<template>
  <view class="job-detail-container">
    <!-- 职位基本信息 -->
    <view class="job-header">
      <view class="job-info">
        <text class="job-title">{{ jobDetail.positionTitle }}</text>
        <text class="salary">{{ formatSalary(jobDetail.salaryMin, jobDetail.salaryMax, jobDetail.salaryUnit) }}</text>
        <view class="job-tags">
          <text class="tag">{{ jobDetail.industry }}</text>
          <text class="tag">{{ jobDetail.workNature }}</text>
          <text class="tag" v-if="jobDetail.isUrgent">紧急</text>
          <text class="tag" v-if="jobDetail.isTop">置顶</text>
        </view>
      </view>
      <view class="company-logo">
        <image
          :src="jobDetail.companyLogo || '/static/default-company.png'"
          mode="aspectFill"
        ></image>
      </view>
    </view>
    
    <!-- 职位要求 -->
    <view class="job-requirements">
      <view class="requirement-item">
        <text class="iconfont icon-location"></text>
        <text>{{ jobDetail.workAddress }}</text>
      </view>
      <view class="requirement-item">
        <text class="iconfont icon-experience"></text>
        <text>{{ jobDetail.workExperience || '经验不限' }}</text>
      </view>
      <view class="requirement-item">
        <text class="iconfont icon-education"></text>
        <text>{{ jobDetail.education || '学历不限' }}</text>
      </view>
      <view class="requirement-item">
        <text class="iconfont icon-time"></text>
        <text>{{ jobDetail.workNature }}</text>
      </view>
    </view>
    
    <!-- 公司信息 -->
    <view class="company-info">
      <view class="company-header">
        <view class="company-basic">
          <text class="company-name">{{ jobDetail.companyName }}</text>
          <text class="company-scale">{{ jobDetail.companyScale }}</text>
        </view>
        <text class="view-company" @click="viewCompany">查看详情</text>
      </view>
      <view class="company-desc">
        <text>{{ jobDetail.companyDesc }}</text>
      </view>
    </view>
    
    <!-- 职位描述 -->
    <view class="job-description">
      <view class="section-title">职位描述</view>
      <view class="description-content">
        <rich-text :nodes="jobDetail.jobDescription"></rich-text>
      </view>
    </view>
    
    <!-- 职位要求 -->
    <view class="job-requirements-detail" v-if="jobDetail.jobRequirements">
      <view class="section-title">职位要求</view>
      <view class="requirements-content">
        <rich-text :nodes="jobDetail.jobRequirements"></rich-text>
      </view>
    </view>

    <!-- 薪资待遇 -->
    <view class="welfare-benefits" v-if="jobDetail.welfareBenefits">
      <view class="section-title">薪资待遇</view>
      <view class="benefits-content">
        <rich-text :nodes="jobDetail.welfareBenefits"></rich-text>
      </view>
    </view>
    
    <!-- 工作地址 -->
    <view class="work-address" v-if="jobDetail.workAddress">
      <view class="section-title">工作地址</view>
      <view class="address-info">
        <view class="address-text">
          <text class="iconfont icon-location"></text>
          <text>{{ jobDetail.workAddress }}</text>
        </view>
        <text class="view-map" @click="viewMap" v-if="jobDetail.latitude && jobDetail.longitude">查看地图</text>
      </view>
    </view>
    
    <!-- 发布信息 -->
    <view class="publish-info">
      <text class="publish-time">发布时间：{{ formatTime(jobDetail.publishTime) }}</text>
      <text class="view-count">浏览：{{ jobDetail.viewCount }}次</text>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="collect-btn" :class="{ collected: isCollected }" @click="toggleCollect">
        <text class="iconfont" :class="isCollected ? 'icon-heart-fill' : 'icon-heart'"></text>
        <text>{{ isCollected ? '已收藏' : '收藏' }}</text>
      </button>
      <button class="contact-btn" @click="viewContact">
        <text class="iconfont icon-phone"></text>
        <text>查看联系方式</text>
      </button>
    </view>
    
    <!-- 查看联系方式弹窗 -->
    <u-popup v-model="showContactModal" mode="center" width="80%" border-radius="20">
      <view class="contact-modal">
        <view class="modal-header">
          <text class="modal-title">联系方式</text>
          <text class="close-btn" @click="showContactModal = false">×</text>
        </view>
        
        <view v-if="contactInfo.visible" class="contact-content">
          <view class="contact-item">
            <text class="label">联系人：</text>
            <text class="value">{{ contactInfo.contactName }}</text>
          </view>
          <view class="contact-item">
            <text class="label">电话：</text>
            <text class="value phone" @click="makeCall(contactInfo.phone)">{{ contactInfo.phone }}</text>
          </view>
          <view class="contact-item" v-if="contactInfo.wechat">
            <text class="label">微信：</text>
            <text class="value">{{ contactInfo.wechat }}</text>
          </view>
        </view>
        
        <view v-else class="payment-content">
          <view class="payment-info">
            <text class="payment-title">查看联系方式</text>
            <text class="payment-desc">支付1元即可查看该职位的联系方式</text>
          </view>
          
          <view class="membership-tip" v-if="!membershipInfo.isMember">
            <text class="tip-text">成为会员更划算</text>
            <view class="membership-options">
              <view class="membership-item" @click="buyMembership('basic')">
                <text class="price">¥30</text>
                <text class="desc">查看45个联系人</text>
              </view>
              <view class="membership-item" @click="buyMembership('premium')">
                <text class="price">¥50</text>
                <text class="desc">查看80个联系人</text>
              </view>
            </view>
          </view>
          
          <view class="payment-actions">
            <button class="pay-btn" @click="payToView" :loading="payLoading">
              {{ membershipInfo.isMember ? '使用会员次数查看' : '支付1元查看' }}
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { jobApi, paymentApi } from '@/utils/api.js'
import { formatSalary, formatTime, showToast, showLoading, hideLoading, auth } from '@/utils/utils.js'

export default {
  name: 'JobDetail',
  data() {
    return {
      jobId: '',
      jobDetail: {},
      isCollected: false,
      showContactModal: false,
      contactInfo: {
        visible: false,
        contactName: '',
        phone: '',
        wechat: ''
      },
      membershipInfo: {
        isMember: false,
        remainingViews: 0
      },
      payLoading: false
    }
  },
  onLoad(options) {
    this.jobId = options.id
    if (this.jobId) {
      this.loadJobDetail()
      this.loadMembershipInfo()
    }
  },
  onShareAppMessage() {
    return {
      title: this.jobDetail.positionTitle,
      path: `/pages/job/detail?id=${this.jobId}`
    }
  },
  methods: {
    formatSalary,
    formatTime,
    
    // 加载职位详情
    async loadJobDetail() {
      try {
        showLoading()
        const res = await jobApi.getJobDetail(this.jobId)
        
        if (res.code === 200) {
          this.jobDetail = res.data
          // 设置页面标题
          uni.setNavigationBarTitle({
            title: this.jobDetail.positionTitle
          })
        } else {
          showToast(res.msg || '加载失败')
        }
      } catch (error) {
        console.error('加载职位详情失败:', error)
        showToast('加载失败，请重试')
      } finally {
        hideLoading()
      }
    },
    
    // 加载会员信息
    async loadMembershipInfo() {
      try {
        const res = await paymentApi.getMembershipInfo()
        if (res.code === 200) {
          this.membershipInfo = res.data
        }
      } catch (error) {
        console.error('加载会员信息失败:', error)
      }
    },
    
    // 查看公司详情
    viewCompany() {
      uni.navigateTo({
        url: `/pages/company/detail?id=${this.jobDetail.companyId}`
      })
    },
    
    // 查看地图
    viewMap() {
      if (!this.jobDetail.latitude || !this.jobDetail.longitude) {
        showToast('暂无位置信息')
        return
      }

      uni.openLocation({
        latitude: parseFloat(this.jobDetail.latitude),
        longitude: parseFloat(this.jobDetail.longitude),
        name: this.jobDetail.companyName,
        address: this.jobDetail.workAddress
      })
    },
    
    // 切换收藏状态
    async toggleCollect() {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      try {
        // 这里调用收藏/取消收藏接口
        this.isCollected = !this.isCollected
        showToast(this.isCollected ? '收藏成功' : '取消收藏')
      } catch (error) {
        showToast('操作失败')
      }
    },
    
    // 查看联系方式
    viewContact() {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      this.showContactModal = true
    },
    
    // 支付查看联系方式
    async payToView() {
      if (this.payLoading) return
      
      try {
        this.payLoading = true
        
        const res = await paymentApi.viewContact(this.jobId)
        
        if (res.code === 200) {
          this.contactInfo = {
            visible: true,
            ...res.data
          }
          
          // 更新会员信息
          if (this.membershipInfo.isMember) {
            this.membershipInfo.remainingViews--
          }
          
          showToast('获取成功')
        } else {
          showToast(res.msg || '获取失败')
        }
      } catch (error) {
        console.error('获取联系方式失败:', error)
        showToast('获取失败，请重试')
      } finally {
        this.payLoading = false
      }
    },
    
    // 购买会员
    async buyMembership(type) {
      try {
        const res = await paymentApi.buyMembership(type)
        
        if (res.code === 200) {
          // 调用微信支付
          await this.wechatPay(res.data)
        } else {
          showToast(res.msg || '购买失败')
        }
      } catch (error) {
        console.error('购买会员失败:', error)
        showToast('购买失败，请重试')
      }
    },
    
    // 微信支付
    async wechatPay(paymentData) {
      return new Promise((resolve, reject) => {
        uni.requestPayment({
          provider: 'wxpay',
          timeStamp: paymentData.timeStamp,
          nonceStr: paymentData.nonceStr,
          package: paymentData.package,
          signType: paymentData.signType,
          paySign: paymentData.paySign,
          success: (res) => {
            showToast('支付成功', 'success')
            this.loadMembershipInfo()
            resolve(res)
          },
          fail: (err) => {
            showToast('支付失败')
            reject(err)
          }
        })
      })
    },
    
    // 拨打电话
    makeCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.job-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
  
  .job-header {
    background: #fff;
    padding: 40rpx;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    
    .job-info {
      flex: 1;
      
      .job-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .salary {
        display: block;
        font-size: 32rpx;
        color: #ff4757;
        font-weight: 600;
        margin-bottom: 24rpx;
      }
      
      .job-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        
        .tag {
          padding: 8rpx 16rpx;
          background: #f0f8ff;
          color: #007aff;
          font-size: 24rpx;
          border-radius: 8rpx;
        }
      }
    }
    
    .company-logo {
      width: 100rpx;
      height: 100rpx;
      border-radius: 16rpx;
      overflow: hidden;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .job-requirements {
    background: #fff;
    padding: 32rpx 40rpx;
    margin-top: 20rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 32rpx;
    
    .requirement-item {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      
      .iconfont {
        font-size: 28rpx;
        margin-right: 12rpx;
        color: #999;
      }
    }
  }
  
  .company-info {
    background: #fff;
    padding: 32rpx 40rpx;
    margin-top: 20rpx;
    
    .company-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;
      
      .company-basic {
        .company-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-right: 16rpx;
        }
        
        .company-scale {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .view-company {
        font-size: 28rpx;
        color: #007aff;
      }
    }
    
    .company-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
  
  .job-description, .job-requirements-detail {
    background: #fff;
    padding: 32rpx 40rpx;
    margin-top: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .description-content, .requirements-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.8;
    }
  }
  
  .work-address {
    background: #fff;
    padding: 32rpx 40rpx;
    margin-top: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .address-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .address-text {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666;
        
        .iconfont {
          font-size: 28rpx;
          margin-right: 12rpx;
          color: #999;
        }
      }
      
      .view-map {
        font-size: 28rpx;
        color: #007aff;
      }
    }
  }
  
  .publish-info {
    background: #fff;
    padding: 32rpx 40rpx;
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
  }
  
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding: 24rpx 40rpx;
    background: #fff;
    border-top: 2rpx solid #f0f0f0;
    
    .collect-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 80rpx;
      background: transparent;
      border: none;
      font-size: 24rpx;
      color: #666;
      margin-right: 32rpx;
      
      &.collected {
        color: #ff4757;
      }
      
      .iconfont {
        font-size: 32rpx;
        margin-bottom: 8rpx;
      }
    }
    
    .contact-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80rpx;
      background: #007aff;
      color: #fff;
      border: none;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: 500;
      
      .iconfont {
        font-size: 32rpx;
        margin-right: 12rpx;
      }
    }
  }
}

.contact-modal {
  padding: 40rpx;
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
  
  .contact-content {
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      
      .label {
        width: 120rpx;
        font-size: 28rpx;
        color: #666;
      }
      
      .value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        
        &.phone {
          color: #007aff;
          text-decoration: underline;
        }
      }
    }
  }
  
  .payment-content {
    text-align: center;
    
    .payment-info {
      margin-bottom: 40rpx;
      
      .payment-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .payment-desc {
        display: block;
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .membership-tip {
      margin-bottom: 40rpx;
      
      .tip-text {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 24rpx;
      }
      
      .membership-options {
        display: flex;
        gap: 24rpx;
        
        .membership-item {
          flex: 1;
          padding: 24rpx;
          background: #f8f9fa;
          border-radius: 12rpx;
          border: 2rpx solid #e9ecef;
          
          .price {
            display: block;
            font-size: 32rpx;
            font-weight: 600;
            color: #ff4757;
            margin-bottom: 8rpx;
          }
          
          .desc {
            display: block;
            font-size: 24rpx;
            color: #666;
          }
        }
      }
    }
    
    .payment-actions {
      .pay-btn {
        width: 100%;
        height: 80rpx;
        background: #007aff;
        color: #fff;
        border: none;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
