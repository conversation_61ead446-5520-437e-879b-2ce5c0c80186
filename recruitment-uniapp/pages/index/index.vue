<template>
  <view class="job-list-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input" @click="goToSearch">
        <text class="iconfont icon-search"></text>
        <text class="placeholder">搜索职位、公司</text>
      </view>
      <view class="location-btn" @click="showLocationPicker = true">
        <text class="location-text">{{ currentLocation }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showSortPicker = true">
        <text>{{ sortText }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
      <view class="filter-item" @click="showSalaryFilter = true">
        <text>{{ salaryFilterText }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
      <view class="filter-item" @click="showIndustryFilter = true">
        <text>{{ industryFilterText }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
      <view class="filter-item" @click="showJobTypeFilter = true">
        <text>{{ jobTypeFilterText }}</text>
        <text class="iconfont icon-arrow-down"></text>
      </view>
    </view>

    <!-- 职位列表 -->
    <scroll-view class="job-list" scroll-y @scrolltolower="loadMore" refresher-enabled :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh">
      <view v-for="job in jobList" :key="job.id" class="job-item" @click="goToJobDetail(job.id)">
        <view class="job-header">
          <view class="job-info">
            <text class="job-title">{{ job.title }}</text>
            <text class="salary">{{ formatSalary(job.minSalary, job.maxSalary, job.salaryType) }}</text>
          </view>
          <view class="company-logo">
            <image :src="job.companyLogo || '/static/default-company.png'" mode="aspectFill"></image>
          </view>
        </view>

        <view class="job-tags">
          <text v-for="tag in job.tags" :key="tag" class="tag">
            {{ tag }}
          </text>
        </view>

        <view class="job-meta">
          <view class="meta-item">
            <text class="iconfont icon-location"></text>
            <text>{{ job.location }}</text>
          </view>
          <view class="meta-item">
            <text class="iconfont icon-experience"></text>
            <text>{{ job.experience }}</text>
          </view>
          <view class="meta-item">
            <text class="iconfont icon-education"></text>
            <text>{{ job.education }}</text>
          </view>
        </view>

        <view class="job-footer">
          <view class="company-info">
            <text class="company-name">{{ job.companyName }}</text>
            <text class="company-scale">{{ job.companyScale }}</text>
          </view>
          <view class="publish-time">
            <text>{{ formatTime(job.publishTime) }}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <text>{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>
      <view v-else-if="jobList.length > 0" class="no-more">
        <text>没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && jobList.length === 0" class="empty-state">
        <text class="empty-text">暂无招聘信息</text>
        <button class="refresh-btn" @click="onRefresh">刷新试试</button>
      </view>
    </scroll-view>

    <!-- 地区选择弹窗 -->
    <u-popup v-model="showLocationPicker" mode="bottom" height="60%" border-radius="20">
      <view class="location-picker">
        <view class="picker-header">
          <text class="cancel-btn" @click="showLocationPicker = false">取消</text>
          <text class="title">选择地区</text>
          <text class="confirm-btn" @click="confirmLocation">确定</text>
        </view>
        <RegionPicker v-model="selectedLocation" />
      </view>
    </u-popup>

    <!-- 排序选择弹窗 -->
    <u-popup v-model="showSortPicker" mode="bottom" height="40%" border-radius="20">
      <view class="sort-picker">
        <view class="picker-header">
          <text class="title">排序方式</text>
        </view>
        <view class="sort-options">
          <view v-for="option in sortOptions" :key="option.value" class="sort-option"
            :class="{ active: sortBy === option.value }" @click="selectSort(option.value)">
            <text>{{ option.label }}</text>
            <text v-if="sortBy === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 薪资筛选弹窗 -->
    <u-popup v-model="showSalaryFilter" mode="bottom" height="50%" border-radius="20">
      <view class="salary-filter">
        <view class="picker-header">
          <text class="cancel-btn" @click="showSalaryFilter = false">取消</text>
          <text class="title">薪资范围</text>
          <text class="confirm-btn" @click="confirmSalaryFilter">确定</text>
        </view>
        <view class="salary-options">
          <view v-for="option in salaryOptions" :key="option.value" class="salary-option"
            :class="{ active: salaryRange === option.value }" @click="selectSalaryRange(option.value)">
            <text>{{ option.label }}</text>
            <text v-if="salaryRange === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 行业筛选弹窗 -->
    <u-popup v-model="showIndustryFilter" mode="bottom" height="60%" border-radius="20">
      <view class="industry-filter">
        <view class="picker-header">
          <text class="cancel-btn" @click="showIndustryFilter = false">取消</text>
          <text class="title">选择行业</text>
          <text class="confirm-btn" @click="confirmIndustryFilter">确定</text>
        </view>
        <view class="industry-options">
          <view v-for="option in industryOptions" :key="option.value" class="industry-option"
            :class="{ active: industryFilter === option.value }" @click="selectIndustry(option.value)">
            <text>{{ option.label }}</text>
            <text v-if="industryFilter === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 工作性质筛选弹窗 -->
    <u-popup v-model="showJobTypeFilter" mode="bottom" height="40%" border-radius="20">
      <view class="job-type-filter">
        <view class="picker-header">
          <text class="cancel-btn" @click="showJobTypeFilter = false">取消</text>
          <text class="title">工作性质</text>
          <text class="confirm-btn" @click="confirmJobTypeFilter">确定</text>
        </view>
        <view class="job-type-options">
          <view v-for="option in jobTypeOptions" :key="option.value" class="job-type-option"
            :class="{ active: jobTypeFilter === option.value }" @click="selectJobType(option.value)">
            <text>{{ option.label }}</text>
            <text v-if="jobTypeFilter === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import RegionPicker from '@/components/RegionPicker.vue'
import { jobApi } from '@/utils/api.js'
import { formatSalary, formatTime, showToast, showLoading, hideLoading, storage } from '@/utils/utils.js'

export default {
  name: 'JobList',
  components: {
    RegionPicker
  },
  data() {
    return {
      jobList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 10,

      // 筛选条件
      sortBy: 'publishTime', // publishTime, salary, distance
      salaryRange: '',
      industryFilter: '',
      jobTypeFilter: '',

      // 地区相关
      currentLocation: '全国',
      selectedLocation: {},
      showLocationPicker: false,

      // 弹窗显示状态
      showSortPicker: false,
      showSalaryFilter: false,
      showIndustryFilter: false,
      showJobTypeFilter: false,

      // 选项数据
      sortOptions: [
        { label: '发布时间', value: 'publishTime' },
        { label: '薪资高低', value: 'salary' },
        { label: '距离远近', value: 'distance' }
      ],
      salaryOptions: [
        { label: '不限', value: '' },
        { label: '3K以下', value: '0-3000' },
        { label: '3K-5K', value: '3000-5000' },
        { label: '5K-8K', value: '5000-8000' },
        { label: '8K-12K', value: '8000-12000' },
        { label: '12K-20K', value: '12000-20000' },
        { label: '20K以上', value: '20000-999999' }
      ],
      industryOptions: [
        { label: '不限', value: '' },
        { label: 'IT/互联网', value: 'IT/互联网' },
        { label: '金融/银行', value: '金融/银行' },
        { label: '教育/培训', value: '教育/培训' },
        { label: '医疗/健康', value: '医疗/健康' },
        { label: '房地产', value: '房地产' },
        { label: '制造业', value: '制造业' },
        { label: '零售/贸易', value: '零售/贸易' },
        { label: '餐饮/服务', value: '餐饮/服务' },
        { label: '物流/运输', value: '物流/运输' },
        { label: '广告/媒体', value: '广告/媒体' },
        { label: '其他', value: '其他' }
      ],
      jobTypeOptions: [
        { label: '不限', value: '' },
        { label: '全职', value: '全职' },
        { label: '兼职', value: '兼职' },
        { label: '实习', value: '实习' },
        { label: '临时', value: '临时' }
      ]
    }
  },
  computed: {
    sortText() {
      const option = this.sortOptions.find(item => item.value === this.sortBy)
      return option ? option.label : '排序'
    },
    salaryFilterText() {
      const option = this.salaryOptions.find(item => item.value === this.salaryRange)
      return option ? option.label : '薪资'
    },
    industryFilterText() {
      return this.industryFilter || '行业'
    },
    jobTypeFilterText() {
      return this.jobTypeFilter || '性质'
    }
  },
  onLoad() {
    this.initLocation()
    this.loadJobList()
  },
  onShow() {
    // 从搜索页返回时刷新列表
    if (this.needRefresh) {
      this.onRefresh()
      this.needRefresh = false
    }
  },
  methods: {
    formatSalary,
    formatTime,

    // 初始化地区
    initLocation() {
      const savedLocation = storage.get('currentLocation')
      if (savedLocation) {
        this.currentLocation = savedLocation.name || '全国'
        this.selectedLocation = savedLocation
      }
    },

    // 加载职位列表
    async loadJobList(isRefresh = false) {
      if (this.loading) return

      try {
        this.loading = true
        if (isRefresh) {
          this.pageNum = 1
          this.hasMore = true
        }

        // 解析薪资范围
        let salaryMin = '', salaryMax = ''
        if (this.salaryRange && this.salaryRange.includes('-')) {
          const [min, max] = this.salaryRange.split('-')
          salaryMin = min
          salaryMax = max
        }

        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          sortBy: this.sortBy,
          salaryMin: salaryMin,
          salaryMax: salaryMax,
          industry: this.industryFilter,
          workNature: this.jobTypeFilter,
          regionCode: (this.selectedLocation.district && this.selectedLocation.district.code) ||
            (this.selectedLocation.city && this.selectedLocation.city.code) ||
            (this.selectedLocation.province && this.selectedLocation.province.code)
        }

        // 如果需要距离排序，获取当前位置
        if (this.sortBy === 'distance') {
          try {
            const location = await this.getCurrentLocation()
            params.latitude = location.latitude
            params.longitude = location.longitude
          } catch (error) {
            console.warn('获取位置失败，使用默认排序:', error)
            params.sortBy = 'publishTime'
          }
        }

        const res = await jobApi.getJobList(params)

        if (res.code === 200) {
          const newList = res.data.list || []

          if (isRefresh) {
            this.jobList = newList
          } else {
            this.jobList = [...this.jobList, ...newList]
          }

          this.hasMore = newList.length === this.pageSize
          this.pageNum++
        } else {
          showToast(res.msg || '加载失败')
        }
      } catch (error) {
        console.error('加载职位列表失败:', error)
        showToast('加载失败，请重试')
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 刷新
    onRefresh() {
      this.refreshing = true
      this.loadJobList(true)
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadJobList()
      }
    },

    // 跳转到搜索页
    goToSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      })
    },

    // 跳转到职位详情
    goToJobDetail(jobId) {
      uni.navigateTo({
        url: `/pages/job/detail?id=${jobId}`
      })
    },

    // 确认地区选择
    confirmLocation() {
      const locationText = (this.selectedLocation.district && this.selectedLocation.district.name) ||
        (this.selectedLocation.city && this.selectedLocation.city.name) ||
        (this.selectedLocation.province && this.selectedLocation.province.name) ||
        '全国'

      this.currentLocation = locationText
      storage.set('currentLocation', {
        name: locationText,
        ...this.selectedLocation
      })

      this.showLocationPicker = false
      this.onRefresh()
    },

    // 选择排序方式
    selectSort(value) {
      this.sortBy = value
      this.showSortPicker = false
      this.onRefresh()
    },

    // 选择薪资范围
    selectSalaryRange(value) {
      this.salaryRange = value
    },

    // 确认薪资筛选
    confirmSalaryFilter() {
      this.showSalaryFilter = false
      this.onRefresh()
    },

    // 选择行业
    selectIndustry(value) {
      this.industryFilter = value
    },

    // 确认行业筛选
    confirmIndustryFilter() {
      this.showIndustryFilter = false
      this.onRefresh()
    },

    // 选择工作性质
    selectJobType(value) {
      this.jobTypeFilter = value
    },

    // 确认工作性质筛选
    confirmJobTypeFilter() {
      this.showJobTypeFilter = false
      this.onRefresh()
    },

    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            resolve({
              latitude: res.latitude,
              longitude: res.longitude
            })
          },
          fail: (error) => {
            reject(error)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.job-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;

  .search-bar {
    display: flex;
    align-items: center;
    padding: 20rpx 32rpx;
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;

    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      height: 72rpx;
      padding: 0 24rpx;
      background: #f8f9fa;
      border-radius: 36rpx;
      margin-right: 20rpx;

      .iconfont {
        font-size: 32rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .placeholder {
        font-size: 28rpx;
        color: #999;
      }
    }

    .location-btn {
      display: flex;
      align-items: center;
      padding: 0 16rpx;

      .location-text {
        font-size: 28rpx;
        color: #333;
        margin-right: 8rpx;
      }

      .iconfont {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .filter-bar {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;

    .filter-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #333;

      .iconfont {
        font-size: 20rpx;
        color: #999;
        margin-left: 8rpx;
      }
    }
  }

  .job-list {
    flex: 1;
    padding: 0 32rpx;

    .job-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin: 24rpx 0;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

      .job-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 24rpx;

        .job-info {
          flex: 1;

          .job-title {
            display: block;
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 12rpx;
          }

          .salary {
            display: block;
            font-size: 28rpx;
            color: #ff4757;
            font-weight: 600;
          }
        }

        .company-logo {
          width: 80rpx;
          height: 80rpx;
          border-radius: 12rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
          }
        }
      }

      .job-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-bottom: 24rpx;

        .tag {
          padding: 8rpx 16rpx;
          background: #f0f8ff;
          color: #007aff;
          font-size: 24rpx;
          border-radius: 8rpx;
        }
      }

      .job-meta {
        display: flex;
        align-items: center;
        gap: 32rpx;
        margin-bottom: 24rpx;

        .meta-item {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #666;

          .iconfont {
            font-size: 24rpx;
            margin-right: 8rpx;
          }
        }
      }

      .job-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .company-info {
          .company-name {
            font-size: 28rpx;
            color: #333;
            margin-right: 16rpx;
          }

          .company-scale {
            font-size: 24rpx;
            color: #666;
          }
        }

        .publish-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .load-more,
    .no-more {
      text-align: center;
      padding: 40rpx 0;
      font-size: 28rpx;
      color: #999;
    }

    .empty-state {
      text-align: center;
      padding: 120rpx 0;

      .empty-img {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 40rpx;
      }

      .empty-text {
        display: block;
        font-size: 28rpx;
        color: #999;
        margin-bottom: 40rpx;
      }

      .refresh-btn {
        width: 200rpx;
        height: 64rpx;
        background: #007aff;
        color: #fff;
        border: none;
        border-radius: 32rpx;
        font-size: 28rpx;
      }
    }
  }
}

// 弹窗样式
.location-picker,
.sort-picker,
.salary-filter {
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .cancel-btn,
    .confirm-btn {
      font-size: 28rpx;
      color: #007aff;
    }

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }
}

.sort-options,
.salary-options {

  .sort-option,
  .salary-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 2rpx solid #f8f9fa;
    font-size: 28rpx;
    color: #333;

    &.active {
      color: #007aff;
      background: #f0f8ff;
    }

    .iconfont {
      font-size: 32rpx;
      color: #007aff;
    }
  }
}
</style>
