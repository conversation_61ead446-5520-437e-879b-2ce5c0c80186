<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecMerchantMapper">
    
    <resultMap type="RecMerchant" id="RecMerchantResult">
        <result property="merchantId"    column="merchant_id"    />
        <result property="userId"    column="user_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyType"    column="company_type"    />
        <result property="businessLicense"    column="business_license"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="districtId"    column="district_id"    />
        <result property="address"    column="address"    />
        <result property="companyDesc"    column="company_desc"    />
        <result property="companyLogo"    column="company_logo"    />
        <result property="companyScale"    column="company_scale"    />
        <result property="industry"    column="industry"    />
        <result property="authStatus"    column="auth_status"    />
        <result property="authTime"    column="auth_time"    />
        <result property="authRemark"    column="auth_remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="districtName"    column="district_name"    />
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectRecMerchantVo">
        select m.merchant_id, m.user_id, m.company_name, m.company_type, m.business_license, 
               m.legal_person, m.contact_person, m.contact_phone, m.contact_email, 
               m.province_id, m.city_id, m.district_id, m.address, m.company_desc, 
               m.company_logo, m.company_scale, m.industry, m.auth_status, m.auth_time, 
               m.auth_remark, m.status, m.create_by, m.create_time, m.update_by, m.update_time,
               p.region_name as province_name,
               c.region_name as city_name,
               d.region_name as district_name,
               u.user_name
        from rec_merchant m
        left join rec_region p on m.province_id = p.region_id
        left join rec_region c on m.city_id = c.region_id
        left join rec_region d on m.district_id = d.region_id
        left join sys_user u on m.user_id = u.user_id
    </sql>

    <select id="selectRecMerchantList" parameterType="RecMerchant" resultMap="RecMerchantResult">
        <include refid="selectRecMerchantVo"/>
        <where>  
            <if test="userId != null "> and m.user_id = #{userId}</if>
            <if test="companyName != null  and companyName != ''"> and m.company_name like concat('%', #{companyName}, '%')</if>
            <if test="companyType != null  and companyType != ''"> and m.company_type = #{companyType}</if>
            <if test="businessLicense != null  and businessLicense != ''"> and m.business_license = #{businessLicense}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and m.legal_person like concat('%', #{legalPerson}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and m.contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and m.contact_phone = #{contactPhone}</if>
            <if test="contactEmail != null  and contactEmail != ''"> and m.contact_email = #{contactEmail}</if>
            <if test="provinceId != null "> and m.province_id = #{provinceId}</if>
            <if test="cityId != null "> and m.city_id = #{cityId}</if>
            <if test="districtId != null "> and m.district_id = #{districtId}</if>
            <if test="companyScale != null  and companyScale != ''"> and m.company_scale = #{companyScale}</if>
            <if test="industry != null  and industry != ''"> and m.industry = #{industry}</if>
            <if test="authStatus != null  and authStatus != ''"> and m.auth_status = #{authStatus}</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
        </where>
        order by m.create_time desc
    </select>
    
    <select id="selectRecMerchantByMerchantId" parameterType="Long" resultMap="RecMerchantResult">
        <include refid="selectRecMerchantVo"/>
        where m.merchant_id = #{merchantId}
    </select>

    <select id="selectRecMerchantByUserId" parameterType="Long" resultMap="RecMerchantResult">
        <include refid="selectRecMerchantVo"/>
        where m.user_id = #{userId}
    </select>

    <select id="selectRecMerchantByAuthStatus" parameterType="String" resultMap="RecMerchantResult">
        <include refid="selectRecMerchantVo"/>
        where m.auth_status = #{authStatus}
        order by m.create_time desc
    </select>

    <select id="selectRecMerchantByBusinessLicense" parameterType="String" resultMap="RecMerchantResult">
        <include refid="selectRecMerchantVo"/>
        where m.business_license = #{businessLicense}
    </select>
        
    <insert id="insertRecMerchant" parameterType="RecMerchant" useGeneratedKeys="true" keyProperty="merchantId">
        insert into rec_merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="companyType != null">company_type,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="cityId != null">city_id,</if>
            <if test="districtId != null">district_id,</if>
            <if test="address != null">address,</if>
            <if test="companyDesc != null">company_desc,</if>
            <if test="companyLogo != null">company_logo,</if>
            <if test="companyScale != null">company_scale,</if>
            <if test="industry != null">industry,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="authTime != null">auth_time,</if>
            <if test="authRemark != null">auth_remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="districtId != null">#{districtId},</if>
            <if test="address != null">#{address},</if>
            <if test="companyDesc != null">#{companyDesc},</if>
            <if test="companyLogo != null">#{companyLogo},</if>
            <if test="companyScale != null">#{companyScale},</if>
            <if test="industry != null">#{industry},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="authTime != null">#{authTime},</if>
            <if test="authRemark != null">#{authRemark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecMerchant" parameterType="RecMerchant">
        update rec_merchant
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="districtId != null">district_id = #{districtId},</if>
            <if test="address != null">address = #{address},</if>
            <if test="companyDesc != null">company_desc = #{companyDesc},</if>
            <if test="companyLogo != null">company_logo = #{companyLogo},</if>
            <if test="companyScale != null">company_scale = #{companyScale},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="authTime != null">auth_time = #{authTime},</if>
            <if test="authRemark != null">auth_remark = #{authRemark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where merchant_id = #{merchantId}
    </update>

    <update id="auditMerchant" parameterType="RecMerchant">
        update rec_merchant
        set auth_status = #{authStatus},
            auth_time = #{authTime},
            auth_remark = #{authRemark},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where merchant_id = #{merchantId}
    </update>

    <update id="updateMerchantStatus" parameterType="RecMerchant">
        update rec_merchant
        set status = #{status},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where merchant_id = #{merchantId}
    </update>

    <delete id="deleteRecMerchantByMerchantId" parameterType="Long">
        delete from rec_merchant where merchant_id = #{merchantId}
    </delete>

    <delete id="deleteRecMerchantByMerchantIds" parameterType="String">
        delete from rec_merchant where merchant_id in 
        <foreach item="merchantId" collection="array" open="(" separator="," close=")">
            #{merchantId}
        </foreach>
    </delete>
</mapper>
