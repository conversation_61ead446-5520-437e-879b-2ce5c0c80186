<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecRegionMapper">
    
    <resultMap type="RecRegion" id="RecRegionResult">
        <result property="regionId"    column="region_id"    />
        <result property="regionCode"    column="region_code"    />
        <result property="regionName"    column="region_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="level"    column="level"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRecRegionVo">
        select region_id, region_code, region_name, parent_id, level, sort_order, status, create_by, create_time, update_by, update_time from rec_region
    </sql>

    <select id="selectRecRegionList" parameterType="RecRegion" resultMap="RecRegionResult">
        <include refid="selectRecRegionVo"/>
        <where>  
            <if test="regionCode != null  and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="regionName != null  and regionName != ''"> and region_name like concat('%', #{regionName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by level, sort_order, region_id
    </select>
    
    <select id="selectRecRegionByRegionId" parameterType="Long" resultMap="RecRegionResult">
        <include refid="selectRecRegionVo"/>
        where region_id = #{regionId}
    </select>

    <select id="selectRecRegionByParentId" parameterType="Long" resultMap="RecRegionResult">
        <include refid="selectRecRegionVo"/>
        where parent_id = #{parentId} and status = '0'
        order by sort_order, region_id
    </select>

    <select id="selectRecRegionByLevel" parameterType="Integer" resultMap="RecRegionResult">
        <include refid="selectRecRegionVo"/>
        where level = #{level} and status = '0'
        order by sort_order, region_id
    </select>

    <select id="selectRecRegionByCode" parameterType="String" resultMap="RecRegionResult">
        <include refid="selectRecRegionVo"/>
        where region_code = #{regionCode}
    </select>
        
    <insert id="insertRecRegion" parameterType="RecRegion" useGeneratedKeys="true" keyProperty="regionId">
        insert into rec_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="level != null">level,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="regionName != null and regionName != ''">#{regionName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="level != null">#{level},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecRegion" parameterType="RecRegion">
        update rec_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="level != null">level = #{level},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where region_id = #{regionId}
    </update>

    <delete id="deleteRecRegionByRegionId" parameterType="Long">
        delete from rec_region where region_id = #{regionId}
    </delete>

    <delete id="deleteRecRegionByRegionIds" parameterType="String">
        delete from rec_region where region_id in 
        <foreach item="regionId" collection="array" open="(" separator="," close=")">
            #{regionId}
        </foreach>
    </delete>
</mapper>
