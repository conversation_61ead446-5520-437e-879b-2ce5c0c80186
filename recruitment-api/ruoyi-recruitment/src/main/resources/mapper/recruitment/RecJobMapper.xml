<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecJobMapper">
    
    <resultMap type="RecJob" id="RecJobResult">
        <result property="jobId"    column="job_id"    />
        <result property="merchantId"    column="merchant_id"    />
        <result property="jobTitle"    column="job_title"    />
        <result property="jobCategory"    column="job_category"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="districtId"    column="district_id"    />
        <result property="workAddress"    column="work_address"    />
        <result property="salaryMin"    column="salary_min"    />
        <result property="salaryMax"    column="salary_max"    />
        <result property="salaryType"    column="salary_type"    />
        <result property="education"    column="education"    />
        <result property="experience"    column="experience"    />
        <result property="jobDesc"    column="job_desc"    />
        <result property="jobRequirements"    column="job_requirements"    />
        <result property="welfare"    column="welfare"    />
        <result property="workType"    column="work_type"    />
        <result property="recruitNum"    column="recruit_num"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="viewCount"    column="view_count"    />
        <result property="applyCount"    column="apply_count"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="districtName"    column="district_name"    />
        <result property="companyName"    column="company_name"    />
    </resultMap>

    <sql id="selectRecJobVo">
        select j.job_id, j.merchant_id, j.job_title, j.job_category, j.province_id, j.city_id, j.district_id, 
               j.work_address, j.salary_min, j.salary_max, j.salary_type, j.education, j.experience, 
               j.job_desc, j.job_requirements, j.welfare, j.work_type, j.recruit_num, j.contact_person, 
               j.contact_phone, j.publish_status, j.audit_status, j.audit_time, j.audit_remark, 
               j.publish_time, j.expire_time, j.view_count, j.apply_count, j.status, 
               j.create_by, j.create_time, j.update_by, j.update_time,
               p.region_name as province_name,
               c.region_name as city_name,
               d.region_name as district_name,
               m.company_name
        from rec_job j
        left join rec_region p on j.province_id = p.region_id
        left join rec_region c on j.city_id = c.region_id
        left join rec_region d on j.district_id = d.region_id
        left join rec_merchant m on j.merchant_id = m.merchant_id
    </sql>

    <select id="selectRecJobList" parameterType="RecJob" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        <where>  
            <if test="merchantId != null "> and j.merchant_id = #{merchantId}</if>
            <if test="jobTitle != null  and jobTitle != ''"> and j.job_title like concat('%', #{jobTitle}, '%')</if>
            <if test="jobCategory != null  and jobCategory != ''"> and j.job_category = #{jobCategory}</if>
            <if test="provinceId != null "> and j.province_id = #{provinceId}</if>
            <if test="cityId != null "> and j.city_id = #{cityId}</if>
            <if test="districtId != null "> and j.district_id = #{districtId}</if>
            <if test="salaryMin != null "> and j.salary_min >= #{salaryMin}</if>
            <if test="salaryMax != null "> and j.salary_max <= #{salaryMax}</if>
            <if test="salaryType != null  and salaryType != ''"> and j.salary_type = #{salaryType}</if>
            <if test="education != null  and education != ''"> and j.education = #{education}</if>
            <if test="experience != null  and experience != ''"> and j.experience = #{experience}</if>
            <if test="workType != null  and workType != ''"> and j.work_type = #{workType}</if>
            <if test="publishStatus != null  and publishStatus != ''"> and j.publish_status = #{publishStatus}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and j.audit_status = #{auditStatus}</if>
            <if test="status != null  and status != ''"> and j.status = #{status}</if>
        </where>
        order by j.create_time desc
    </select>
    
    <select id="selectRecJobByJobId" parameterType="Long" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.job_id = #{jobId}
    </select>

    <select id="selectRecJobByMerchantId" parameterType="Long" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.merchant_id = #{merchantId}
        order by j.create_time desc
    </select>

    <select id="selectRecJobByPublishStatus" parameterType="String" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = #{publishStatus}
        order by j.create_time desc
    </select>

    <select id="selectRecJobByAuditStatus" parameterType="String" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.audit_status = #{auditStatus}
        order by j.create_time desc
    </select>

    <select id="selectPublishedJobList" parameterType="RecJob" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        <if test="jobTitle != null  and jobTitle != ''"> and j.job_title like concat('%', #{jobTitle}, '%')</if>
        <if test="jobCategory != null  and jobCategory != ''"> and j.job_category = #{jobCategory}</if>
        <if test="provinceId != null "> and j.province_id = #{provinceId}</if>
        <if test="cityId != null "> and j.city_id = #{cityId}</if>
        <if test="districtId != null "> and j.district_id = #{districtId}</if>
        <if test="salaryMin != null "> and j.salary_min >= #{salaryMin}</if>
        <if test="salaryMax != null "> and j.salary_max <= #{salaryMax}</if>
        <if test="education != null  and education != ''"> and j.education = #{education}</if>
        <if test="experience != null  and experience != ''"> and j.experience = #{experience}</if>
        <if test="workType != null  and workType != ''"> and j.work_type = #{workType}</if>
        order by j.publish_time desc
    </select>

    <select id="selectHotJobList" parameterType="Integer" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        order by j.view_count desc, j.apply_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectLatestJobList" parameterType="Integer" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        order by j.publish_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectJobByRegion" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        <if test="provinceId != null">
            and j.province_id = #{provinceId}
        </if>
        <if test="cityId != null">
            and j.city_id = #{cityId}
        </if>
        <if test="districtId != null">
            and j.district_id = #{districtId}
        </if>
        order by j.publish_time desc
    </select>

    <select id="selectJobByCategory" parameterType="String" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        and j.job_category = #{jobCategory}
        order by j.publish_time desc
    </select>

    <select id="searchJobs" parameterType="String" resultMap="RecJobResult">
        <include refid="selectRecJobVo"/>
        where j.publish_status = '2' and j.audit_status = '1' and j.status = '0' 
        and (j.expire_time is null or j.expire_time > now())
        and (j.job_title like concat('%', #{keyword}, '%') 
             or j.job_desc like concat('%', #{keyword}, '%')
             or j.job_requirements like concat('%', #{keyword}, '%')
             or m.company_name like concat('%', #{keyword}, '%'))
        order by j.publish_time desc
    </select>

    <insert id="insertRecJob" parameterType="RecJob" useGeneratedKeys="true" keyProperty="jobId">
        insert into rec_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">merchant_id,</if>
            <if test="jobTitle != null and jobTitle != ''">job_title,</if>
            <if test="jobCategory != null">job_category,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="cityId != null">city_id,</if>
            <if test="districtId != null">district_id,</if>
            <if test="workAddress != null">work_address,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="salaryType != null">salary_type,</if>
            <if test="education != null">education,</if>
            <if test="experience != null">experience,</if>
            <if test="jobDesc != null">job_desc,</if>
            <if test="jobRequirements != null">job_requirements,</if>
            <if test="welfare != null">welfare,</if>
            <if test="workType != null">work_type,</if>
            <if test="recruitNum != null">recruit_num,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="applyCount != null">apply_count,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">#{merchantId},</if>
            <if test="jobTitle != null and jobTitle != ''">#{jobTitle},</if>
            <if test="jobCategory != null">#{jobCategory},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="districtId != null">#{districtId},</if>
            <if test="workAddress != null">#{workAddress},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="salaryType != null">#{salaryType},</if>
            <if test="education != null">#{education},</if>
            <if test="experience != null">#{experience},</if>
            <if test="jobDesc != null">#{jobDesc},</if>
            <if test="jobRequirements != null">#{jobRequirements},</if>
            <if test="welfare != null">#{welfare},</if>
            <if test="workType != null">#{workType},</if>
            <if test="recruitNum != null">#{recruitNum},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="applyCount != null">#{applyCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecJob" parameterType="RecJob">
        update rec_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="jobTitle != null and jobTitle != ''">job_title = #{jobTitle},</if>
            <if test="jobCategory != null">job_category = #{jobCategory},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="districtId != null">district_id = #{districtId},</if>
            <if test="workAddress != null">work_address = #{workAddress},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="salaryType != null">salary_type = #{salaryType},</if>
            <if test="education != null">education = #{education},</if>
            <if test="experience != null">experience = #{experience},</if>
            <if test="jobDesc != null">job_desc = #{jobDesc},</if>
            <if test="jobRequirements != null">job_requirements = #{jobRequirements},</if>
            <if test="welfare != null">welfare = #{welfare},</if>
            <if test="workType != null">work_type = #{workType},</if>
            <if test="recruitNum != null">recruit_num = #{recruitNum},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="applyCount != null">apply_count = #{applyCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where job_id = #{jobId}
    </update>

    <update id="auditJob" parameterType="RecJob">
        update rec_job
        set audit_status = #{auditStatus},
            audit_time = #{auditTime},
            audit_remark = #{auditRemark},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where job_id = #{jobId}
    </update>

    <update id="updateJobPublishStatus" parameterType="RecJob">
        update rec_job
        set publish_status = #{publishStatus},
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            update_by = #{updateBy},
            update_time = #{updateTime}
        where job_id = #{jobId}
    </update>

    <update id="increaseViewCount" parameterType="Long">
        update rec_job set view_count = view_count + 1 where job_id = #{jobId}
    </update>

    <update id="increaseApplyCount" parameterType="Long">
        update rec_job set apply_count = apply_count + 1 where job_id = #{jobId}
    </update>

    <delete id="deleteRecJobByJobId" parameterType="Long">
        delete from rec_job where job_id = #{jobId}
    </delete>

    <delete id="deleteRecJobByJobIds" parameterType="String">
        delete from rec_job where job_id in
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>
</mapper>
