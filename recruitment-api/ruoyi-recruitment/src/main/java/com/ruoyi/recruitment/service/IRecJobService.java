package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJob;

/**
 * 职位信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecJobService 
{
    /**
     * 查询职位信息
     * 
     * @param jobId 职位信息主键
     * @return 职位信息
     */
    public RecJob selectRecJobByJobId(Long jobId);

    /**
     * 查询职位信息列表
     * 
     * @param recJob 职位信息
     * @return 职位信息集合
     */
    public List<RecJob> selectRecJobList(RecJob recJob);

    /**
     * 根据商家ID查询职位信息列表
     * 
     * @param merchantId 商家ID
     * @return 职位信息集合
     */
    public List<RecJob> selectRecJobByMerchantId(Long merchantId);

    /**
     * 根据发布状态查询职位信息列表
     * 
     * @param publishStatus 发布状态
     * @return 职位信息集合
     */
    public List<RecJob> selectRecJobByPublishStatus(String publishStatus);

    /**
     * 根据审核状态查询职位信息列表
     * 
     * @param auditStatus 审核状态
     * @return 职位信息集合
     */
    public List<RecJob> selectRecJobByAuditStatus(String auditStatus);

    /**
     * 新增职位信息
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    public int insertRecJob(RecJob recJob);

    /**
     * 修改职位信息
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    public int updateRecJob(RecJob recJob);

    /**
     * 批量删除职位信息
     * 
     * @param jobIds 需要删除的职位信息主键集合
     * @return 结果
     */
    public int deleteRecJobByJobIds(Long[] jobIds);

    /**
     * 删除职位信息信息
     * 
     * @param jobId 职位信息主键
     * @return 结果
     */
    public int deleteRecJobByJobId(Long jobId);

    /**
     * 发布职位
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    public int publishJob(RecJob recJob);

    /**
     * 审核职位信息
     * 
     * @param jobId 职位ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditJob(Long jobId, String auditStatus, String auditRemark);

    /**
     * 更新职位发布状态
     * 
     * @param jobId 职位ID
     * @param publishStatus 发布状态
     * @return 结果
     */
    public int updateJobPublishStatus(Long jobId, String publishStatus);

    /**
     * 增加职位浏览次数
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    public int increaseViewCount(Long jobId);

    /**
     * 增加职位申请次数
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    public int increaseApplyCount(Long jobId);

    /**
     * 查询已发布的职位信息列表（前端展示用）
     * 
     * @param recJob 职位信息
     * @return 职位信息集合
     */
    public List<RecJob> selectPublishedJobList(RecJob recJob);

    /**
     * 查询热门职位列表
     * 
     * @param limit 限制数量
     * @return 职位信息集合
     */
    public List<RecJob> selectHotJobList(Integer limit);

    /**
     * 查询最新职位列表
     * 
     * @param limit 限制数量
     * @return 职位信息集合
     */
    public List<RecJob> selectLatestJobList(Integer limit);

    /**
     * 根据地区查询职位信息列表
     * 
     * @param provinceId 省份ID
     * @param cityId 城市ID
     * @param districtId 区县ID
     * @return 职位信息集合
     */
    public List<RecJob> selectJobByRegion(Long provinceId, Long cityId, Long districtId);

    /**
     * 根据职位类别查询职位信息列表
     * 
     * @param jobCategory 职位类别
     * @return 职位信息集合
     */
    public List<RecJob> selectJobByCategory(String jobCategory);

    /**
     * 搜索职位信息
     * 
     * @param keyword 关键词
     * @return 职位信息集合
     */
    public List<RecJob> searchJobs(String keyword);

    /**
     * 获取待审核职位列表
     * 
     * @return 职位信息集合
     */
    public List<RecJob> getPendingAuditJobs();

    /**
     * 下线职位
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    public int offlineJob(Long jobId);

    /**
     * 重新发布职位
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    public int republishJob(Long jobId);
}
