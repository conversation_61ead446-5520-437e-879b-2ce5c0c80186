package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 职位信息对象 rec_job
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class RecJob extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 职位ID */
    private Long jobId;

    /** 商家ID */
    @Excel(name = "商家ID")
    private Long merchantId;

    /** 职位标题 */
    @Excel(name = "职位标题")
    private String jobTitle;

    /** 职位类别 */
    @Excel(name = "职位类别")
    private String jobCategory;

    /** 省份ID */
    @Excel(name = "省份ID")
    private Long provinceId;

    /** 城市ID */
    @Excel(name = "城市ID")
    private Long cityId;

    /** 区县ID */
    @Excel(name = "区县ID")
    private Long districtId;

    /** 工作地址 */
    @Excel(name = "工作地址")
    private String workAddress;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private BigDecimal salaryMax;

    /** 薪资类型（1月薪 2年薪 3日薪 4时薪） */
    @Excel(name = "薪资类型", readConverterExp = "1=月薪,2=年薪,3=日薪,4=时薪")
    private String salaryType;

    /** 学历要求 */
    @Excel(name = "学历要求")
    private String education;

    /** 工作经验 */
    @Excel(name = "工作经验")
    private String experience;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String jobDesc;

    /** 职位要求 */
    @Excel(name = "职位要求")
    private String jobRequirements;

    /** 福利待遇 */
    @Excel(name = "福利待遇")
    private String welfare;

    /** 工作类型（1全职 2兼职 3实习） */
    @Excel(name = "工作类型", readConverterExp = "1=全职,2=兼职,3=实习")
    private String workType;

    /** 招聘人数 */
    @Excel(name = "招聘人数")
    private Integer recruitNum;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 发布状态（0草稿 1待审核 2已发布 3已下线） */
    @Excel(name = "发布状态", readConverterExp = "0=草稿,1=待审核,2=已发布,3=已下线")
    private String publishStatus;

    /** 审核状态（0待审核 1已通过 2已拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝")
    private String auditStatus;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    private Integer applyCount;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 省份名称 */
    private String provinceName;

    /** 城市名称 */
    private String cityName;

    /** 区县名称 */
    private String districtName;

    /** 商家名称 */
    private String companyName;

    public void setJobId(Long jobId) 
    {
        this.jobId = jobId;
    }

    public Long getJobId() 
    {
        return jobId;
    }
    public void setMerchantId(Long merchantId) 
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }
    public void setJobTitle(String jobTitle) 
    {
        this.jobTitle = jobTitle;
    }

    public String getJobTitle() 
    {
        return jobTitle;
    }
    public void setJobCategory(String jobCategory) 
    {
        this.jobCategory = jobCategory;
    }

    public String getJobCategory() 
    {
        return jobCategory;
    }
    public void setProvinceId(Long provinceId) 
    {
        this.provinceId = provinceId;
    }

    public Long getProvinceId() 
    {
        return provinceId;
    }
    public void setCityId(Long cityId) 
    {
        this.cityId = cityId;
    }

    public Long getCityId() 
    {
        return cityId;
    }
    public void setDistrictId(Long districtId) 
    {
        this.districtId = districtId;
    }

    public Long getDistrictId() 
    {
        return districtId;
    }
    public void setWorkAddress(String workAddress) 
    {
        this.workAddress = workAddress;
    }

    public String getWorkAddress() 
    {
        return workAddress;
    }
    public void setSalaryMin(BigDecimal salaryMin) 
    {
        this.salaryMin = salaryMin;
    }

    public BigDecimal getSalaryMin() 
    {
        return salaryMin;
    }
    public void setSalaryMax(BigDecimal salaryMax) 
    {
        this.salaryMax = salaryMax;
    }

    public BigDecimal getSalaryMax() 
    {
        return salaryMax;
    }
    public void setSalaryType(String salaryType) 
    {
        this.salaryType = salaryType;
    }

    public String getSalaryType() 
    {
        return salaryType;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setExperience(String experience) 
    {
        this.experience = experience;
    }

    public String getExperience() 
    {
        return experience;
    }
    public void setJobDesc(String jobDesc) 
    {
        this.jobDesc = jobDesc;
    }

    public String getJobDesc() 
    {
        return jobDesc;
    }
    public void setJobRequirements(String jobRequirements) 
    {
        this.jobRequirements = jobRequirements;
    }

    public String getJobRequirements() 
    {
        return jobRequirements;
    }
    public void setWelfare(String welfare) 
    {
        this.welfare = welfare;
    }

    public String getWelfare() 
    {
        return welfare;
    }
    public void setWorkType(String workType) 
    {
        this.workType = workType;
    }

    public String getWorkType() 
    {
        return workType;
    }
    public void setRecruitNum(Integer recruitNum) 
    {
        this.recruitNum = recruitNum;
    }

    public Integer getRecruitNum() 
    {
        return recruitNum;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setPublishStatus(String publishStatus) 
    {
        this.publishStatus = publishStatus;
    }

    public String getPublishStatus() 
    {
        return publishStatus;
    }
    public void setAuditStatus(String auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }
    public void setPublishTime(Date publishTime) 
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() 
    {
        return publishTime;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setApplyCount(Integer applyCount) 
    {
        this.applyCount = applyCount;
    }

    public Integer getApplyCount() 
    {
        return applyCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("jobId", getJobId())
            .append("merchantId", getMerchantId())
            .append("jobTitle", getJobTitle())
            .append("jobCategory", getJobCategory())
            .append("provinceId", getProvinceId())
            .append("cityId", getCityId())
            .append("districtId", getDistrictId())
            .append("workAddress", getWorkAddress())
            .append("salaryMin", getSalaryMin())
            .append("salaryMax", getSalaryMax())
            .append("salaryType", getSalaryType())
            .append("education", getEducation())
            .append("experience", getExperience())
            .append("jobDesc", getJobDesc())
            .append("jobRequirements", getJobRequirements())
            .append("welfare", getWelfare())
            .append("workType", getWorkType())
            .append("recruitNum", getRecruitNum())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("publishStatus", getPublishStatus())
            .append("auditStatus", getAuditStatus())
            .append("auditTime", getAuditTime())
            .append("auditRemark", getAuditRemark())
            .append("publishTime", getPublishTime())
            .append("expireTime", getExpireTime())
            .append("viewCount", getViewCount())
            .append("applyCount", getApplyCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
