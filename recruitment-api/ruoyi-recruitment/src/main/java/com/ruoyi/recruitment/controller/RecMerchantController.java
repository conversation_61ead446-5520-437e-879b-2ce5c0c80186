package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecMerchant;
import com.ruoyi.recruitment.service.IRecMerchantService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 商家信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/merchant")
public class RecMerchantController extends BaseController
{
    @Autowired
    private IRecMerchantService recMerchantService;

    /**
     * 查询商家信息列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecMerchant recMerchant)
    {
        startPage();
        List<RecMerchant> list = recMerchantService.selectRecMerchantList(recMerchant);
        return getDataTable(list);
    }

    /**
     * 导出商家信息列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:export')")
    @Log(title = "商家信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecMerchant recMerchant)
    {
        List<RecMerchant> list = recMerchantService.selectRecMerchantList(recMerchant);
        ExcelUtil<RecMerchant> util = new ExcelUtil<RecMerchant>(RecMerchant.class);
        util.exportExcel(response, list, "商家信息数据");
    }

    /**
     * 获取商家信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:query')")
    @GetMapping(value = "/{merchantId}")
    public AjaxResult getInfo(@PathVariable("merchantId") Long merchantId)
    {
        return success(recMerchantService.selectRecMerchantByMerchantId(merchantId));
    }

    /**
     * 新增商家信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:add')")
    @Log(title = "商家信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecMerchant recMerchant)
    {
        return toAjax(recMerchantService.insertRecMerchant(recMerchant));
    }

    /**
     * 修改商家信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:edit')")
    @Log(title = "商家信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecMerchant recMerchant)
    {
        return toAjax(recMerchantService.updateRecMerchant(recMerchant));
    }

    /**
     * 删除商家信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:remove')")
    @Log(title = "商家信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{merchantIds}")
    public AjaxResult remove(@PathVariable Long[] merchantIds)
    {
        return toAjax(recMerchantService.deleteRecMerchantByMerchantIds(merchantIds));
    }

    /**
     * 商家注册入驻
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody RecMerchant recMerchant)
    {
        try {
            // 设置当前用户ID
            recMerchant.setUserId(SecurityUtils.getUserId());
            int result = recMerchantService.registerMerchant(recMerchant);
            return result > 0 ? success("注册成功，请等待审核") : error("注册失败");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 获取当前用户的商家信息
     */
    @GetMapping("/profile")
    public AjaxResult getProfile()
    {
        Long userId = SecurityUtils.getUserId();
        RecMerchant merchant = recMerchantService.selectRecMerchantByUserId(userId);
        return success(merchant);
    }

    /**
     * 更新商家资料
     */
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody RecMerchant recMerchant)
    {
        Long userId = SecurityUtils.getUserId();
        RecMerchant existMerchant = recMerchantService.selectRecMerchantByUserId(userId);
        
        if (existMerchant == null) {
            return error("商家信息不存在");
        }

        // 只允许修改自己的商家信息
        recMerchant.setMerchantId(existMerchant.getMerchantId());
        recMerchant.setUserId(userId);
        
        return toAjax(recMerchantService.updateRecMerchant(recMerchant));
    }

    /**
     * 商家审核
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:audit')")
    @Log(title = "商家审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{merchantId}")
    public AjaxResult audit(@PathVariable Long merchantId, @RequestBody RecMerchant recMerchant)
    {
        int result = recMerchantService.auditMerchant(merchantId, recMerchant.getAuthStatus(), recMerchant.getAuthRemark());
        return toAjax(result);
    }

    /**
     * 更新商家状态
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:status')")
    @Log(title = "商家状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{merchantId}/{status}")
    public AjaxResult updateStatus(@PathVariable Long merchantId, @PathVariable String status)
    {
        int result = recMerchantService.updateMerchantStatus(merchantId, status);
        return toAjax(result);
    }

    /**
     * 获取待审核商家列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:merchant:audit')")
    @GetMapping("/pending")
    public TableDataInfo getPendingAudit()
    {
        startPage();
        List<RecMerchant> list = recMerchantService.getPendingAuditMerchants();
        return getDataTable(list);
    }

    /**
     * 获取已通过认证的商家列表
     */
    @GetMapping("/approved")
    public TableDataInfo getApproved()
    {
        startPage();
        List<RecMerchant> list = recMerchantService.getApprovedMerchants();
        return getDataTable(list);
    }

    /**
     * 检查营业执照是否已存在
     */
    @GetMapping("/check/license/{businessLicense}")
    public AjaxResult checkBusinessLicense(@PathVariable String businessLicense)
    {
        boolean exists = recMerchantService.checkBusinessLicenseExists(businessLicense);
        return success(exists);
    }

    /**
     * 检查用户是否已注册商家
     */
    @GetMapping("/check/user")
    public AjaxResult checkUserMerchant()
    {
        Long userId = SecurityUtils.getUserId();
        boolean exists = recMerchantService.checkUserMerchantExists(userId);
        return success(exists);
    }
}
