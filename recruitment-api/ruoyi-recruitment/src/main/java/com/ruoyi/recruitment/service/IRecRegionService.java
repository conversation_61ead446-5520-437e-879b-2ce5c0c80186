package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecRegion;

/**
 * 地区Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecRegionService 
{
    /**
     * 查询地区
     * 
     * @param regionId 地区主键
     * @return 地区
     */
    public RecRegion selectRecRegionByRegionId(Long regionId);

    /**
     * 查询地区列表
     * 
     * @param recRegion 地区
     * @return 地区集合
     */
    public List<RecRegion> selectRecRegionList(RecRegion recRegion);

    /**
     * 根据父级ID查询地区列表
     * 
     * @param parentId 父级ID
     * @return 地区集合
     */
    public List<RecRegion> selectRecRegionByParentId(Long parentId);

    /**
     * 根据级别查询地区列表
     * 
     * @param level 级别
     * @return 地区集合
     */
    public List<RecRegion> selectRecRegionByLevel(Integer level);

    /**
     * 新增地区
     * 
     * @param recRegion 地区
     * @return 结果
     */
    public int insertRecRegion(RecRegion recRegion);

    /**
     * 修改地区
     * 
     * @param recRegion 地区
     * @return 结果
     */
    public int updateRecRegion(RecRegion recRegion);

    /**
     * 批量删除地区
     * 
     * @param regionIds 需要删除的地区主键集合
     * @return 结果
     */
    public int deleteRecRegionByRegionIds(Long[] regionIds);

    /**
     * 删除地区信息
     * 
     * @param regionId 地区主键
     * @return 结果
     */
    public int deleteRecRegionByRegionId(Long regionId);

    /**
     * 从公共API同步地区数据
     * 
     * @return 结果
     */
    public boolean syncRegionData();

    /**
     * 获取省份列表
     * 
     * @return 省份列表
     */
    public List<RecRegion> getProvinceList();

    /**
     * 获取城市列表
     * 
     * @param provinceId 省份ID
     * @return 城市列表
     */
    public List<RecRegion> getCityList(Long provinceId);

    /**
     * 获取区县列表
     * 
     * @param cityId 城市ID
     * @return 区县列表
     */
    public List<RecRegion> getDistrictList(Long cityId);

    /**
     * 根据地区编码查询地区
     * 
     * @param regionCode 地区编码
     * @return 地区
     */
    public RecRegion selectRecRegionByCode(String regionCode);
}
