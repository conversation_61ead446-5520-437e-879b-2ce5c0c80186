package com.ruoyi.recruitment.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商家信息对象 rec_merchant
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class RecMerchant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商家ID */
    private Long merchantId;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private Long userId;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司类型 */
    @Excel(name = "公司类型")
    private String companyType;

    /** 营业执照号 */
    @Excel(name = "营业执照号")
    private String businessLicense;

    /** 法人代表 */
    @Excel(name = "法人代表")
    private String legalPerson;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 省份ID */
    @Excel(name = "省份ID")
    private Long provinceId;

    /** 城市ID */
    @Excel(name = "城市ID")
    private Long cityId;

    /** 区县ID */
    @Excel(name = "区县ID")
    private Long districtId;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 公司描述 */
    @Excel(name = "公司描述")
    private String companyDesc;

    /** 公司logo */
    @Excel(name = "公司logo")
    private String companyLogo;

    /** 公司规模 */
    @Excel(name = "公司规模")
    private String companyScale;

    /** 所属行业 */
    @Excel(name = "所属行业")
    private String industry;

    /** 认证状态（0待审核 1已通过 2已拒绝） */
    @Excel(name = "认证状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝")
    private String authStatus;

    /** 认证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "认证时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date authTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String authRemark;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 省份名称 */
    private String provinceName;

    /** 城市名称 */
    private String cityName;

    /** 区县名称 */
    private String districtName;

    /** 用户名 */
    private String userName;

    public void setMerchantId(Long merchantId) 
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setCompanyType(String companyType) 
    {
        this.companyType = companyType;
    }

    public String getCompanyType() 
    {
        return companyType;
    }
    public void setBusinessLicense(String businessLicense) 
    {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense() 
    {
        return businessLicense;
    }
    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setProvinceId(Long provinceId) 
    {
        this.provinceId = provinceId;
    }

    public Long getProvinceId() 
    {
        return provinceId;
    }
    public void setCityId(Long cityId) 
    {
        this.cityId = cityId;
    }

    public Long getCityId() 
    {
        return cityId;
    }
    public void setDistrictId(Long districtId) 
    {
        this.districtId = districtId;
    }

    public Long getDistrictId() 
    {
        return districtId;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setCompanyDesc(String companyDesc) 
    {
        this.companyDesc = companyDesc;
    }

    public String getCompanyDesc() 
    {
        return companyDesc;
    }
    public void setCompanyLogo(String companyLogo) 
    {
        this.companyLogo = companyLogo;
    }

    public String getCompanyLogo() 
    {
        return companyLogo;
    }
    public void setCompanyScale(String companyScale) 
    {
        this.companyScale = companyScale;
    }

    public String getCompanyScale() 
    {
        return companyScale;
    }
    public void setIndustry(String industry) 
    {
        this.industry = industry;
    }

    public String getIndustry() 
    {
        return industry;
    }
    public void setAuthStatus(String authStatus) 
    {
        this.authStatus = authStatus;
    }

    public String getAuthStatus() 
    {
        return authStatus;
    }
    public void setAuthTime(Date authTime) 
    {
        this.authTime = authTime;
    }

    public Date getAuthTime() 
    {
        return authTime;
    }
    public void setAuthRemark(String authRemark) 
    {
        this.authRemark = authRemark;
    }

    public String getAuthRemark() 
    {
        return authRemark;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("merchantId", getMerchantId())
            .append("userId", getUserId())
            .append("companyName", getCompanyName())
            .append("companyType", getCompanyType())
            .append("businessLicense", getBusinessLicense())
            .append("legalPerson", getLegalPerson())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("provinceId", getProvinceId())
            .append("cityId", getCityId())
            .append("districtId", getDistrictId())
            .append("address", getAddress())
            .append("companyDesc", getCompanyDesc())
            .append("companyLogo", getCompanyLogo())
            .append("companyScale", getCompanyScale())
            .append("industry", getIndustry())
            .append("authStatus", getAuthStatus())
            .append("authTime", getAuthTime())
            .append("authRemark", getAuthRemark())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
