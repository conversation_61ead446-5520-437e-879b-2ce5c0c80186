package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecMerchant;

/**
 * 商家信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RecMerchantMapper 
{
    /**
     * 查询商家信息
     * 
     * @param merchantId 商家信息主键
     * @return 商家信息
     */
    public RecMerchant selectRecMerchantByMerchantId(Long merchantId);

    /**
     * 查询商家信息列表
     * 
     * @param recMerchant 商家信息
     * @return 商家信息集合
     */
    public List<RecMerchant> selectRecMerchantList(RecMerchant recMerchant);

    /**
     * 根据用户ID查询商家信息
     * 
     * @param userId 用户ID
     * @return 商家信息
     */
    public RecMerchant selectRecMerchantByUserId(Long userId);

    /**
     * 根据认证状态查询商家信息列表
     * 
     * @param authStatus 认证状态
     * @return 商家信息集合
     */
    public List<RecMerchant> selectRecMerchantByAuthStatus(String authStatus);

    /**
     * 新增商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int insertRecMerchant(RecMerchant recMerchant);

    /**
     * 修改商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int updateRecMerchant(RecMerchant recMerchant);

    /**
     * 删除商家信息
     * 
     * @param merchantId 商家信息主键
     * @return 结果
     */
    public int deleteRecMerchantByMerchantId(Long merchantId);

    /**
     * 批量删除商家信息
     * 
     * @param merchantIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecMerchantByMerchantIds(Long[] merchantIds);

    /**
     * 根据营业执照号查询商家信息
     * 
     * @param businessLicense 营业执照号
     * @return 商家信息
     */
    public RecMerchant selectRecMerchantByBusinessLicense(String businessLicense);

    /**
     * 审核商家认证
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int auditMerchant(RecMerchant recMerchant);

    /**
     * 更新商家状态
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int updateMerchantStatus(RecMerchant recMerchant);
}
