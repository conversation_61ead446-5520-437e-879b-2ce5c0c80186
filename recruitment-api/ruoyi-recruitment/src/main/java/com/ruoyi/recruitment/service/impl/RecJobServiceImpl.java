package com.ruoyi.recruitment.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecJobMapper;
import com.ruoyi.recruitment.domain.RecJob;
import com.ruoyi.recruitment.service.IRecJobService;
import com.ruoyi.recruitment.service.IRecSystemConfigService;

/**
 * 职位信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecJobServiceImpl implements IRecJobService 
{
    @Autowired
    private RecJobMapper recJobMapper;

    @Autowired
    private IRecSystemConfigService systemConfigService;

    /**
     * 查询职位信息
     * 
     * @param jobId 职位信息主键
     * @return 职位信息
     */
    @Override
    public RecJob selectRecJobByJobId(Long jobId)
    {
        return recJobMapper.selectRecJobByJobId(jobId);
    }

    /**
     * 查询职位信息列表
     * 
     * @param recJob 职位信息
     * @return 职位信息
     */
    @Override
    public List<RecJob> selectRecJobList(RecJob recJob)
    {
        return recJobMapper.selectRecJobList(recJob);
    }

    /**
     * 根据商家ID查询职位信息列表
     * 
     * @param merchantId 商家ID
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectRecJobByMerchantId(Long merchantId)
    {
        return recJobMapper.selectRecJobByMerchantId(merchantId);
    }

    /**
     * 根据发布状态查询职位信息列表
     * 
     * @param publishStatus 发布状态
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectRecJobByPublishStatus(String publishStatus)
    {
        return recJobMapper.selectRecJobByPublishStatus(publishStatus);
    }

    /**
     * 根据审核状态查询职位信息列表
     * 
     * @param auditStatus 审核状态
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectRecJobByAuditStatus(String auditStatus)
    {
        return recJobMapper.selectRecJobByAuditStatus(auditStatus);
    }

    /**
     * 新增职位信息
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    @Override
    public int insertRecJob(RecJob recJob)
    {
        // 设置默认值
        if (recJob.getPublishStatus() == null) {
            recJob.setPublishStatus("0"); // 草稿
        }
        if (recJob.getAuditStatus() == null) {
            recJob.setAuditStatus("0"); // 待审核
        }
        if (recJob.getStatus() == null) {
            recJob.setStatus("0"); // 正常
        }
        if (recJob.getViewCount() == null) {
            recJob.setViewCount(0);
        }
        if (recJob.getApplyCount() == null) {
            recJob.setApplyCount(0);
        }
        
        recJob.setCreateTime(DateUtils.getNowDate());
        return recJobMapper.insertRecJob(recJob);
    }

    /**
     * 修改职位信息
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    @Override
    public int updateRecJob(RecJob recJob)
    {
        recJob.setUpdateTime(DateUtils.getNowDate());
        return recJobMapper.updateRecJob(recJob);
    }

    /**
     * 批量删除职位信息
     * 
     * @param jobIds 需要删除的职位信息主键
     * @return 结果
     */
    @Override
    public int deleteRecJobByJobIds(Long[] jobIds)
    {
        return recJobMapper.deleteRecJobByJobIds(jobIds);
    }

    /**
     * 删除职位信息信息
     * 
     * @param jobId 职位信息主键
     * @return 结果
     */
    @Override
    public int deleteRecJobByJobId(Long jobId)
    {
        return recJobMapper.deleteRecJobByJobId(jobId);
    }

    /**
     * 发布职位
     * 
     * @param recJob 职位信息
     * @return 结果
     */
    @Override
    public int publishJob(RecJob recJob)
    {
        recJob.setPublishStatus("1"); // 待审核
        recJob.setAuditStatus("0"); // 待审核
        recJob.setUpdateTime(DateUtils.getNowDate());
        recJob.setUpdateBy(SecurityUtils.getUsername());
        
        return recJobMapper.updateRecJob(recJob);
    }

    /**
     * 审核职位信息
     * 
     * @param jobId 职位ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditJob(Long jobId, String auditStatus, String auditRemark)
    {
        RecJob job = new RecJob();
        job.setJobId(jobId);
        job.setAuditStatus(auditStatus);
        job.setAuditTime(new Date());
        job.setAuditRemark(auditRemark);
        job.setUpdateBy(SecurityUtils.getUsername());
        job.setUpdateTime(DateUtils.getNowDate());

        // 如果审核通过，自动发布职位
        if ("1".equals(auditStatus)) {
            job.setPublishStatus("2"); // 已发布
            job.setPublishTime(new Date());
            
            // 设置过期时间
            String expireDaysStr = systemConfigService.selectConfigByKey("job_expire_days");
            int expireDays = 30; // 默认30天
            try {
                expireDays = Integer.parseInt(expireDaysStr);
            } catch (Exception e) {
                // 使用默认值
            }
            
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, expireDays);
            job.setExpireTime(calendar.getTime());
        }

        return recJobMapper.auditJob(job);
    }

    /**
     * 更新职位发布状态
     * 
     * @param jobId 职位ID
     * @param publishStatus 发布状态
     * @return 结果
     */
    @Override
    public int updateJobPublishStatus(Long jobId, String publishStatus)
    {
        RecJob job = new RecJob();
        job.setJobId(jobId);
        job.setPublishStatus(publishStatus);
        job.setUpdateBy(SecurityUtils.getUsername());
        job.setUpdateTime(DateUtils.getNowDate());

        if ("2".equals(publishStatus)) {
            job.setPublishTime(new Date());
            
            // 设置过期时间
            String expireDaysStr = systemConfigService.selectConfigByKey("job_expire_days");
            int expireDays = 30; // 默认30天
            try {
                expireDays = Integer.parseInt(expireDaysStr);
            } catch (Exception e) {
                // 使用默认值
            }
            
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, expireDays);
            job.setExpireTime(calendar.getTime());
        }

        return recJobMapper.updateJobPublishStatus(job);
    }

    /**
     * 增加职位浏览次数
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long jobId)
    {
        return recJobMapper.increaseViewCount(jobId);
    }

    /**
     * 增加职位申请次数
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    @Override
    public int increaseApplyCount(Long jobId)
    {
        return recJobMapper.increaseApplyCount(jobId);
    }

    /**
     * 查询已发布的职位信息列表（前端展示用）
     * 
     * @param recJob 职位信息
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectPublishedJobList(RecJob recJob)
    {
        return recJobMapper.selectPublishedJobList(recJob);
    }

    /**
     * 查询热门职位列表
     * 
     * @param limit 限制数量
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectHotJobList(Integer limit)
    {
        return recJobMapper.selectHotJobList(limit);
    }

    /**
     * 查询最新职位列表
     * 
     * @param limit 限制数量
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectLatestJobList(Integer limit)
    {
        return recJobMapper.selectLatestJobList(limit);
    }

    /**
     * 根据地区查询职位信息列表
     * 
     * @param provinceId 省份ID
     * @param cityId 城市ID
     * @param districtId 区县ID
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectJobByRegion(Long provinceId, Long cityId, Long districtId)
    {
        return recJobMapper.selectJobByRegion(provinceId, cityId, districtId);
    }

    /**
     * 根据职位类别查询职位信息列表
     * 
     * @param jobCategory 职位类别
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> selectJobByCategory(String jobCategory)
    {
        return recJobMapper.selectJobByCategory(jobCategory);
    }

    /**
     * 搜索职位信息
     * 
     * @param keyword 关键词
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> searchJobs(String keyword)
    {
        return recJobMapper.searchJobs(keyword);
    }

    /**
     * 获取待审核职位列表
     * 
     * @return 职位信息集合
     */
    @Override
    public List<RecJob> getPendingAuditJobs()
    {
        return recJobMapper.selectRecJobByAuditStatus("0");
    }

    /**
     * 下线职位
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    @Override
    public int offlineJob(Long jobId)
    {
        return updateJobPublishStatus(jobId, "3"); // 已下线
    }

    /**
     * 重新发布职位
     * 
     * @param jobId 职位ID
     * @return 结果
     */
    @Override
    public int republishJob(Long jobId)
    {
        return updateJobPublishStatus(jobId, "2"); // 已发布
    }
}
