package com.ruoyi.recruitment.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.recruitment.mapper.RecMerchantMapper;
import com.ruoyi.recruitment.domain.RecMerchant;
import com.ruoyi.recruitment.service.IRecMerchantService;

/**
 * 商家信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecMerchantServiceImpl implements IRecMerchantService 
{
    @Autowired
    private RecMerchantMapper recMerchantMapper;

    /**
     * 查询商家信息
     * 
     * @param merchantId 商家信息主键
     * @return 商家信息
     */
    @Override
    public RecMerchant selectRecMerchantByMerchantId(Long merchantId)
    {
        return recMerchantMapper.selectRecMerchantByMerchantId(merchantId);
    }

    /**
     * 查询商家信息列表
     * 
     * @param recMerchant 商家信息
     * @return 商家信息
     */
    @Override
    public List<RecMerchant> selectRecMerchantList(RecMerchant recMerchant)
    {
        return recMerchantMapper.selectRecMerchantList(recMerchant);
    }

    /**
     * 根据用户ID查询商家信息
     * 
     * @param userId 用户ID
     * @return 商家信息
     */
    @Override
    public RecMerchant selectRecMerchantByUserId(Long userId)
    {
        return recMerchantMapper.selectRecMerchantByUserId(userId);
    }

    /**
     * 根据认证状态查询商家信息列表
     * 
     * @param authStatus 认证状态
     * @return 商家信息集合
     */
    @Override
    public List<RecMerchant> selectRecMerchantByAuthStatus(String authStatus)
    {
        return recMerchantMapper.selectRecMerchantByAuthStatus(authStatus);
    }

    /**
     * 新增商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    @Override
    public int insertRecMerchant(RecMerchant recMerchant)
    {
        recMerchant.setCreateTime(DateUtils.getNowDate());
        return recMerchantMapper.insertRecMerchant(recMerchant);
    }

    /**
     * 修改商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    @Override
    public int updateRecMerchant(RecMerchant recMerchant)
    {
        recMerchant.setUpdateTime(DateUtils.getNowDate());
        return recMerchantMapper.updateRecMerchant(recMerchant);
    }

    /**
     * 批量删除商家信息
     * 
     * @param merchantIds 需要删除的商家信息主键
     * @return 结果
     */
    @Override
    public int deleteRecMerchantByMerchantIds(Long[] merchantIds)
    {
        return recMerchantMapper.deleteRecMerchantByMerchantIds(merchantIds);
    }

    /**
     * 删除商家信息信息
     * 
     * @param merchantId 商家信息主键
     * @return 结果
     */
    @Override
    public int deleteRecMerchantByMerchantId(Long merchantId)
    {
        return recMerchantMapper.deleteRecMerchantByMerchantId(merchantId);
    }

    /**
     * 商家注册入驻
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    @Override
    public int registerMerchant(RecMerchant recMerchant)
    {
        // 检查用户是否已注册商家
        if (checkUserMerchantExists(recMerchant.getUserId())) {
            throw new RuntimeException("该用户已注册商家，不能重复注册");
        }

        // 检查营业执照是否已存在
        if (StringUtils.isNotEmpty(recMerchant.getBusinessLicense()) && 
            checkBusinessLicenseExists(recMerchant.getBusinessLicense())) {
            throw new RuntimeException("该营业执照号已被注册");
        }

        // 设置默认值
        recMerchant.setAuthStatus("0"); // 待审核
        recMerchant.setStatus("0"); // 正常
        recMerchant.setCreateBy(SecurityUtils.getUsername());
        recMerchant.setCreateTime(DateUtils.getNowDate());

        return recMerchantMapper.insertRecMerchant(recMerchant);
    }

    /**
     * 审核商家认证
     * 
     * @param merchantId 商家ID
     * @param authStatus 审核状态
     * @param authRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditMerchant(Long merchantId, String authStatus, String authRemark)
    {
        RecMerchant merchant = new RecMerchant();
        merchant.setMerchantId(merchantId);
        merchant.setAuthStatus(authStatus);
        merchant.setAuthTime(new Date());
        merchant.setAuthRemark(authRemark);
        merchant.setUpdateBy(SecurityUtils.getUsername());
        merchant.setUpdateTime(DateUtils.getNowDate());

        return recMerchantMapper.auditMerchant(merchant);
    }

    /**
     * 更新商家状态
     * 
     * @param merchantId 商家ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateMerchantStatus(Long merchantId, String status)
    {
        RecMerchant merchant = new RecMerchant();
        merchant.setMerchantId(merchantId);
        merchant.setStatus(status);
        merchant.setUpdateBy(SecurityUtils.getUsername());
        merchant.setUpdateTime(DateUtils.getNowDate());

        return recMerchantMapper.updateMerchantStatus(merchant);
    }

    /**
     * 检查营业执照是否已存在
     * 
     * @param businessLicense 营业执照号
     * @return 是否存在
     */
    @Override
    public boolean checkBusinessLicenseExists(String businessLicense)
    {
        if (StringUtils.isEmpty(businessLicense)) {
            return false;
        }
        RecMerchant merchant = recMerchantMapper.selectRecMerchantByBusinessLicense(businessLicense);
        return merchant != null;
    }

    /**
     * 检查用户是否已注册商家
     * 
     * @param userId 用户ID
     * @return 是否已注册
     */
    @Override
    public boolean checkUserMerchantExists(Long userId)
    {
        if (userId == null) {
            return false;
        }
        RecMerchant merchant = recMerchantMapper.selectRecMerchantByUserId(userId);
        return merchant != null;
    }

    /**
     * 获取待审核商家列表
     * 
     * @return 商家信息集合
     */
    @Override
    public List<RecMerchant> getPendingAuditMerchants()
    {
        return recMerchantMapper.selectRecMerchantByAuthStatus("0");
    }

    /**
     * 获取已通过认证的商家列表
     * 
     * @return 商家信息集合
     */
    @Override
    public List<RecMerchant> getApprovedMerchants()
    {
        return recMerchantMapper.selectRecMerchantByAuthStatus("1");
    }
}
