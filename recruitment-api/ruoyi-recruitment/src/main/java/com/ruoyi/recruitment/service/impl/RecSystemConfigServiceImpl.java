package com.ruoyi.recruitment.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecSystemConfigMapper;
import com.ruoyi.recruitment.domain.RecSystemConfig;
import com.ruoyi.recruitment.service.IRecSystemConfigService;

/**
 * 系统配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RecSystemConfigServiceImpl implements IRecSystemConfigService 
{
    @Autowired
    private RecSystemConfigMapper recSystemConfigMapper;

    /**
     * 查询系统配置
     * 
     * @param configId 系统配置主键
     * @return 系统配置
     */
    @Override
    public RecSystemConfig selectRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.selectRecSystemConfigByConfigId(configId);
    }

    /**
     * 查询系统配置列表
     * 
     * @param recSystemConfig 系统配置
     * @return 系统配置
     */
    @Override
    public List<RecSystemConfig> selectRecSystemConfigList(RecSystemConfig recSystemConfig)
    {
        return recSystemConfigMapper.selectRecSystemConfigList(recSystemConfig);
    }

    /**
     * 新增系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    public int insertRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setCreateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.insertRecSystemConfig(recSystemConfig);
    }

    /**
     * 修改系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    public int updateRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setUpdateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.updateRecSystemConfig(recSystemConfig);
    }

    /**
     * 批量删除系统配置
     * 
     * @param configIds 需要删除的系统配置主键
     * @return 结果
     */
    @Override
    public int deleteRecSystemConfigByConfigIds(Long[] configIds)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigIds(configIds);
    }

    /**
     * 删除系统配置信息
     * 
     * @param configId 系统配置主键
     * @return 结果
     */
    @Override
    public int deleteRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigId(configId);
    }

    /**
     * 根据配置键查询配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    public String selectConfigByKey(String configKey)
    {
        RecSystemConfig config = recSystemConfigMapper.selectRecSystemConfigByKey(configKey);
        return config != null ? config.getConfigValue() : null;
    }

    /**
     * 根据配置键查询配置信息
     * 
     * @param configKey 配置键
     * @return 系统配置
     */
    @Override
    public RecSystemConfig selectRecSystemConfigByKey(String configKey)
    {
        return recSystemConfigMapper.selectRecSystemConfigByKey(configKey);
    }

    /**
     * 更新配置值
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 结果
     */
    @Override
    public int updateConfigByKey(String configKey, String configValue)
    {
        RecSystemConfig config = new RecSystemConfig();
        config.setConfigKey(configKey);
        config.setConfigValue(configValue);
        config.setUpdateBy(SecurityUtils.getUsername());
        config.setUpdateTime(DateUtils.getNowDate());
        
        return recSystemConfigMapper.updateConfigByKey(config);
    }
}
