package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecRegion;
import com.ruoyi.recruitment.service.IRecRegionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 地区Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/recruitment/region")
public class RecRegionController extends BaseController
{
    @Autowired
    private IRecRegionService recRegionService;

    /**
     * 查询地区列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecRegion recRegion)
    {
        startPage();
        List<RecRegion> list = recRegionService.selectRecRegionList(recRegion);
        return getDataTable(list);
    }

    /**
     * 导出地区列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:export')")
    @Log(title = "地区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecRegion recRegion)
    {
        List<RecRegion> list = recRegionService.selectRecRegionList(recRegion);
        ExcelUtil<RecRegion> util = new ExcelUtil<RecRegion>(RecRegion.class);
        util.exportExcel(response, list, "地区数据");
    }

    /**
     * 获取地区详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:query')")
    @GetMapping(value = "/{regionId}")
    public AjaxResult getInfo(@PathVariable("regionId") Long regionId)
    {
        return success(recRegionService.selectRecRegionByRegionId(regionId));
    }

    /**
     * 新增地区
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:add')")
    @Log(title = "地区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecRegion recRegion)
    {
        return toAjax(recRegionService.insertRecRegion(recRegion));
    }

    /**
     * 修改地区
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:edit')")
    @Log(title = "地区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecRegion recRegion)
    {
        return toAjax(recRegionService.updateRecRegion(recRegion));
    }

    /**
     * 删除地区
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:remove')")
    @Log(title = "地区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{regionIds}")
    public AjaxResult remove(@PathVariable Long[] regionIds)
    {
        return toAjax(recRegionService.deleteRecRegionByRegionIds(regionIds));
    }

    /**
     * 获取省份列表
     */
    @GetMapping("/provinces")
    public AjaxResult getProvinces()
    {
        List<RecRegion> provinces = recRegionService.getProvinceList();
        return success(provinces);
    }

    /**
     * 根据省份ID获取城市列表
     */
    @GetMapping("/cities/{provinceId}")
    public AjaxResult getCities(@PathVariable("provinceId") Long provinceId)
    {
        List<RecRegion> cities = recRegionService.getCityList(provinceId);
        return success(cities);
    }

    /**
     * 根据城市ID获取区县列表
     */
    @GetMapping("/districts/{cityId}")
    public AjaxResult getDistricts(@PathVariable("cityId") Long cityId)
    {
        List<RecRegion> districts = recRegionService.getDistrictList(cityId);
        return success(districts);
    }

    /**
     * 同步地区数据
     */
    @PreAuthorize("@ss.hasPermi('recruitment:region:sync')")
    @Log(title = "地区数据同步", businessType = BusinessType.UPDATE)
    @PostMapping("/sync")
    public AjaxResult syncRegionData()
    {
        boolean result = recRegionService.syncRegionData();
        return result ? success("地区数据同步成功") : error("地区数据同步失败");
    }

    /**
     * 根据父级ID获取子级地区列表
     */
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildren(@PathVariable("parentId") Long parentId)
    {
        List<RecRegion> children = recRegionService.selectRecRegionByParentId(parentId);
        return success(children);
    }
}
