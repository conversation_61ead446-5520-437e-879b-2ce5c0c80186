package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecMerchant;

/**
 * 商家信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRecMerchantService 
{
    /**
     * 查询商家信息
     * 
     * @param merchantId 商家信息主键
     * @return 商家信息
     */
    public RecMerchant selectRecMerchantByMerchantId(Long merchantId);

    /**
     * 查询商家信息列表
     * 
     * @param recMerchant 商家信息
     * @return 商家信息集合
     */
    public List<RecMerchant> selectRecMerchantList(RecMerchant recMerchant);

    /**
     * 根据用户ID查询商家信息
     * 
     * @param userId 用户ID
     * @return 商家信息
     */
    public RecMerchant selectRecMerchantByUserId(Long userId);

    /**
     * 根据认证状态查询商家信息列表
     * 
     * @param authStatus 认证状态
     * @return 商家信息集合
     */
    public List<RecMerchant> selectRecMerchantByAuthStatus(String authStatus);

    /**
     * 新增商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int insertRecMerchant(RecMerchant recMerchant);

    /**
     * 修改商家信息
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int updateRecMerchant(RecMerchant recMerchant);

    /**
     * 批量删除商家信息
     * 
     * @param merchantIds 需要删除的商家信息主键集合
     * @return 结果
     */
    public int deleteRecMerchantByMerchantIds(Long[] merchantIds);

    /**
     * 删除商家信息信息
     * 
     * @param merchantId 商家信息主键
     * @return 结果
     */
    public int deleteRecMerchantByMerchantId(Long merchantId);

    /**
     * 商家注册入驻
     * 
     * @param recMerchant 商家信息
     * @return 结果
     */
    public int registerMerchant(RecMerchant recMerchant);

    /**
     * 审核商家认证
     * 
     * @param merchantId 商家ID
     * @param authStatus 审核状态
     * @param authRemark 审核备注
     * @return 结果
     */
    public int auditMerchant(Long merchantId, String authStatus, String authRemark);

    /**
     * 更新商家状态
     * 
     * @param merchantId 商家ID
     * @param status 状态
     * @return 结果
     */
    public int updateMerchantStatus(Long merchantId, String status);

    /**
     * 检查营业执照是否已存在
     * 
     * @param businessLicense 营业执照号
     * @return 是否存在
     */
    public boolean checkBusinessLicenseExists(String businessLicense);

    /**
     * 检查用户是否已注册商家
     * 
     * @param userId 用户ID
     * @return 是否已注册
     */
    public boolean checkUserMerchantExists(Long userId);

    /**
     * 获取待审核商家列表
     * 
     * @return 商家信息集合
     */
    public List<RecMerchant> getPendingAuditMerchants();

    /**
     * 获取已通过认证的商家列表
     * 
     * @return 商家信息集合
     */
    public List<RecMerchant> getApprovedMerchants();
}
