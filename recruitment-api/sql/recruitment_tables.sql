-- ----------------------------
-- 招聘平台数据库表结构
-- ----------------------------

-- ----------------------------
-- 1、地区表（省市县三级联动）
-- ----------------------------
DROP TABLE IF EXISTS `rec_region`;
CREATE TABLE `rec_region` (
  `region_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `region_code` varchar(20) NOT NULL COMMENT '地区编码',
  `region_name` varchar(50) NOT NULL COMMENT '地区名称',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父级地区ID',
  `level` tinyint(1) NOT NULL COMMENT '地区级别（1省 2市 3县）',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`region_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='地区表';

-- ----------------------------
-- 2、商家信息表
-- ----------------------------
DROP TABLE IF EXISTS `rec_merchant`;
CREATE TABLE `rec_merchant` (
  `merchant_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `company_name` varchar(100) NOT NULL COMMENT '公司名称',
  `company_type` varchar(20) DEFAULT '' COMMENT '公司类型',
  `business_license` varchar(50) DEFAULT '' COMMENT '营业执照号',
  `legal_person` varchar(50) DEFAULT '' COMMENT '法人代表',
  `contact_person` varchar(50) DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT '' COMMENT '联系邮箱',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `district_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
  `address` varchar(200) DEFAULT '' COMMENT '详细地址',
  `company_desc` text COMMENT '公司描述',
  `company_logo` varchar(200) DEFAULT '' COMMENT '公司logo',
  `company_scale` varchar(20) DEFAULT '' COMMENT '公司规模',
  `industry` varchar(50) DEFAULT '' COMMENT '所属行业',
  `auth_status` char(1) DEFAULT '0' COMMENT '认证状态（0待审核 1已通过 2已拒绝）',
  `auth_time` datetime DEFAULT NULL COMMENT '认证时间',
  `auth_remark` varchar(500) DEFAULT '' COMMENT '审核备注',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`merchant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_status` (`auth_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='商家信息表';

-- ----------------------------
-- 3、职位信息表
-- ----------------------------
DROP TABLE IF EXISTS `rec_job`;
CREATE TABLE `rec_job` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `job_title` varchar(100) NOT NULL COMMENT '职位标题',
  `job_category` varchar(50) DEFAULT '' COMMENT '职位类别',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `district_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
  `work_address` varchar(200) DEFAULT '' COMMENT '工作地址',
  `salary_min` decimal(10,2) DEFAULT 0.00 COMMENT '最低薪资',
  `salary_max` decimal(10,2) DEFAULT 0.00 COMMENT '最高薪资',
  `salary_type` char(1) DEFAULT '1' COMMENT '薪资类型（1月薪 2年薪 3日薪 4时薪）',
  `education` varchar(20) DEFAULT '' COMMENT '学历要求',
  `experience` varchar(20) DEFAULT '' COMMENT '工作经验',
  `job_desc` text COMMENT '职位描述',
  `job_requirements` text COMMENT '职位要求',
  `welfare` text COMMENT '福利待遇',
  `work_type` char(1) DEFAULT '1' COMMENT '工作类型（1全职 2兼职 3实习）',
  `recruit_num` int(4) DEFAULT 1 COMMENT '招聘人数',
  `contact_person` varchar(50) DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `publish_status` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1待审核 2已发布 3已下线）',
  `audit_status` char(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已通过 2已拒绝）',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT '' COMMENT '审核备注',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `view_count` int(10) DEFAULT 0 COMMENT '浏览次数',
  `apply_count` int(10) DEFAULT 0 COMMENT '申请次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`job_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_publish_status` (`publish_status`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='职位信息表';

-- ----------------------------
-- 4、用户扩展表（C端用户）
-- ----------------------------
DROP TABLE IF EXISTS `rec_user_profile`;
CREATE TABLE `rec_user_profile` (
  `profile_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户档案ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT '' COMMENT '身份证号',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `education` varchar(20) DEFAULT '' COMMENT '学历',
  `work_experience` int(2) DEFAULT 0 COMMENT '工作经验（年）',
  `current_salary` decimal(10,2) DEFAULT 0.00 COMMENT '当前薪资',
  `expected_salary` decimal(10,2) DEFAULT 0.00 COMMENT '期望薪资',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
  `district_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
  `address` varchar(200) DEFAULT '' COMMENT '详细地址',
  `resume_url` varchar(200) DEFAULT '' COMMENT '简历文件地址',
  `self_intro` text COMMENT '自我介绍',
  `skills` varchar(500) DEFAULT '' COMMENT '技能标签',
  `is_vip` char(1) DEFAULT '0' COMMENT '是否VIP（0否 1是）',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP到期时间',
  `view_contact_count` int(10) DEFAULT 0 COMMENT '查看联系方式次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1冻结）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`profile_id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='用户扩展表';

-- ----------------------------
-- 5、求职申请表
-- ----------------------------
DROP TABLE IF EXISTS `rec_job_application`;
CREATE TABLE `rec_job_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `job_id` bigint(20) NOT NULL COMMENT '职位ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
  `status` char(1) DEFAULT '0' COMMENT '申请状态（0待处理 1已查看 2已沟通 3已拒绝）',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`application_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_id` (`merchant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='求职申请表';

-- ----------------------------
-- 6、订单表
-- ----------------------------
DROP TABLE IF EXISTS `rec_order`;
CREATE TABLE `rec_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_type` char(1) NOT NULL COMMENT '订单类型（1查看联系方式 2VIP会员）',
  `product_name` varchar(100) DEFAULT '' COMMENT '产品名称',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `pay_status` char(1) DEFAULT '0' COMMENT '支付状态（0待支付 1已支付 2已退款）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_method` varchar(20) DEFAULT '' COMMENT '支付方式',
  `transaction_id` varchar(100) DEFAULT '' COMMENT '交易流水号',
  `refund_status` char(1) DEFAULT '0' COMMENT '退款状态（0无退款 1退款中 2已退款）',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_reason` varchar(200) DEFAULT '' COMMENT '退款原因',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1取消）',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='订单表';

-- ----------------------------
-- 7、系统配置表（价格设置等）
-- ----------------------------
DROP TABLE IF EXISTS `rec_system_config`;
CREATE TABLE `rec_system_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) DEFAULT '' COMMENT '配置值',
  `config_name` varchar(100) DEFAULT '' COMMENT '配置名称',
  `config_desc` varchar(200) DEFAULT '' COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='系统配置表';

-- ----------------------------
-- 8、消费记录表
-- ----------------------------
DROP TABLE IF EXISTS `rec_consumption_record`;
CREATE TABLE `rec_consumption_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `consumption_type` char(1) NOT NULL COMMENT '消费类型（1查看联系方式 2购买VIP）',
  `amount` decimal(10,2) NOT NULL COMMENT '消费金额',
  `description` varchar(200) DEFAULT '' COMMENT '消费描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='消费记录表';

-- ----------------------------
-- 初始化系统配置数据
-- ----------------------------
INSERT INTO `rec_system_config` VALUES 
(1, 'view_contact_price', '5.00', '查看联系方式价格', '用户查看企业联系方式的费用', 'decimal', '0', 'admin', NOW(), '', NULL),
(2, 'vip_month_price', '29.00', 'VIP月费', 'VIP会员月费价格', 'decimal', '0', 'admin', NOW(), '', NULL),
(3, 'vip_year_price', '299.00', 'VIP年费', 'VIP会员年费价格', 'decimal', '0', 'admin', NOW(), '', NULL),
(4, 'job_expire_days', '30', '职位有效期', '职位发布后的有效天数', 'int', '0', 'admin', NOW(), '', NULL);
